import type { NextConfig } from 'next';

const nextConfig: NextConfig = {
  // Configure for development and production
  trailingSlash: true,

  // Image optimization settings
  images: {
    unoptimized: process.env.NODE_ENV === 'production',
  },

  // TypeScript and ESLint settings
  typescript: {
    ignoreBuildErrors: false,
  },
  eslint: {
    ignoreDuringBuilds: false,
  },

  // Experimental features
  experimental: {
    esmExternals: true,
  },
};

export default nextConfig;
