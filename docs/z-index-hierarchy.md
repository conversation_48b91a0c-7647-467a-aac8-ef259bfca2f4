# Z-Index Hierarchy

This document defines the z-index hierarchy used throughout the Kavia application to prevent UI overlap issues.

## Z-Index Levels

### Level 1: Base Content (z-index: auto/0)
- Regular page content
- Cards, forms, buttons
- Static elements

### Level 2: Navigation & Fixed Elements (z-index: 90-99)
- Mobile hamburger button: `z-[90]`
- Fixed navigation elements
- Floating action buttons

### Level 3: Dropdowns & Popovers (z-index: 100-109)
- Select dropdowns: `z-[100]`
- Dropdown menus: `z-[100]`
- Popovers: `z-[100]`
- Tooltips: `z-[100]`
- Context menus: `z-[100]`

### Level 4: Sheets & Sidebars (z-index: 150-169)
- Sheet overlays: `z-[150]`
- Sheet content: `z-[160]`
- Mobile sidebars
- Slide-out panels

### Level 5: Modals & Dialogs (z-index: 190-209)
- Dialog overlays: `z-[190]`
- Dialog content: `z-[200]`
- Modal windows
- Alert dialogs

### Level 6: Toast Notifications (z-index: 1000+)
- Toast notifications (handled by Sonner library)
- System-level notifications

## Guidelines

1. **Always use the predefined levels** - Don't create arbitrary z-index values
2. **Test on mobile** - Mobile layouts are more prone to overlap issues
3. **Consider stacking context** - New stacking contexts can affect z-index behavior
4. **Document changes** - Update this file when adding new z-index values

## Common Issues

### Dropdown Overlap
- **Problem**: Dropdowns appearing behind other elements
- **Solution**: Ensure dropdowns use `z-[100]` or higher

### Mobile Sidebar Issues
- **Problem**: Sidebar content appearing behind dropdowns
- **Solution**: Use proper Sheet component z-index hierarchy

### Modal Accessibility
- **Problem**: Elements behind modals still interactive
- **Solution**: Ensure modal overlays have proper z-index and focus management

## Implementation

When implementing new components:

1. Identify the component's category from the levels above
2. Use the appropriate z-index range
3. Test with existing components to ensure no conflicts
4. Update this documentation if adding new categories

## Files Modified

- `src/components/ui/select.tsx` - Select dropdown content
- `src/components/ui/dropdown-menu.tsx` - Dropdown menu content and sub-content
- `src/components/ui/popover.tsx` - Popover content
- `src/components/ui/tooltip.tsx` - Tooltip content
- `src/components/ui/dialog.tsx` - Dialog overlay and content
- `src/components/ui/sheet.tsx` - Sheet overlay and content
- `src/components/sidebar/mobile-hamburger.tsx` - Mobile hamburger button
- `src/components/sidebar/unified-sidebar.tsx` - Removed custom z-index

## Testing

To test the z-index hierarchy:

1. Open the application on mobile
2. Open a dropdown/select menu
3. Verify it appears above all other elements
4. Test with sidebar open/closed
5. Test with multiple overlays (dropdown + modal)
