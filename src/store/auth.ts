import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { AuthState, User, Tenant } from '@/types';
import { decodeJwt } from 'jose';

interface JWTPayload {
  sub: string;
  email: string;
  'custom:Name'?: string;
  'custom:tenant_id': string;
  'custom:is_admin'?: string;
  'custom:is_super_admin'?: string;
  exp: number;
  iat: number;
  [key: string]: unknown;
}

interface AuthStore extends AuthState {
  login: (user: User, tenant: Tenant) => void;
  logout: () => void;
  setLoading: (loading: boolean) => void;
  updateUser: (user: Partial<User>) => void;
  initializeFromJWT: () => void;
  setTokenData: (idToken: string) => void;
}

// Helper function to get cookie value
const getCookie = (name: string): string | undefined => {
  if (typeof document === 'undefined') return undefined;
  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) return parts.pop()?.split(';').shift();
  return undefined;
};

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      tenant: null,

      login: (user: User, tenant: Tenant) => {
        set({
          user,
          tenant,
          isAuthenticated: true,
          isLoading: false,
        });
      },

      logout: () => {
        // Clear the idToken cookie
        if (typeof document !== 'undefined') {
          document.cookie = 'idToken=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
        }
        set({
          user: null,
          tenant: null,
          isAuthenticated: false,
          isLoading: false,
        });
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      updateUser: (userData: Partial<User>) => {
        const currentUser = get().user;
        if (currentUser) {
          set({
            user: { ...currentUser, ...userData },
          });
        }
      },

      initializeFromJWT: () => {
        const idToken = getCookie('idToken');
        if (idToken) {
          get().setTokenData(idToken);
        }
      },

      setTokenData: (idToken: string) => {
        try {
          const decoded = decodeJwt(idToken) as JWTPayload;
          
          // Check if token is expired
          const currentTime = Math.floor(Date.now() / 1000);
          if (decoded.exp < currentTime) {
            // Token expired, logout
            get().logout();
            return;
          }

          // Create user object from JWT
          const user: User = {
            id: decoded.sub,
            email: decoded.email,
            name: decoded['custom:Name'] || '',
            tenantId: decoded['custom:tenant_id'],
            role: decoded['custom:is_admin'] === 'true' ? 'admin' : 'user',
            createdAt: new Date(),
            updatedAt: new Date(),
          };

          // Create tenant object from JWT
          const tenant: Tenant = {
            id: decoded['custom:tenant_id'],
            name: decoded['custom:tenant_id'], // We'll use tenant_id as name for now
            slug: decoded['custom:tenant_id'],
            settings: {
              theme: {
                primaryColor: '#007bff',
              },
              features: {
                enableVoiceInput: false,
                enableFileUpload: true,
              },
            },
            createdAt: new Date(),
            updatedAt: new Date(),
          };

          set({
            user,
            tenant,
            isAuthenticated: true,
            isLoading: false,
          });
        } catch (error) {
          console.error('Error decoding JWT:', error);
          get().logout();
        }
      },
    }),
    {
      name: 'kavia-auth-storage',
      partialize: (state) => ({
        user: state.user,
        tenant: state.tenant,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);
