'use client';

import { create } from 'zustand';

export type ChatRole = 'user' | 'assistant';

export interface ChatMsg {
  id: string;
  role: ChatRole;
  content: string;
  createdAt: Date;
  artifactId?: string;
}

export interface Artifact {
  id: string;
  title: string;
  type: 'react' | 'html' | 'python' | 'javascript' | 'typescript';
  content: string;
  language: string;
  createdAt: Date;
}

type ChatStatus = 'idle' | 'thinking';

interface ChatStore {
  messages: ChatMsg[];
  status: ChatStatus;
  artifacts: Artifact[];
  activeArtifactId: string | null;
  artifactPanelOpen: boolean;
  artifactPanelWidth: number;
  currentChatId: string | null;

  startNewChat: (text: string, files?: File[]) => void;
  sendMessage: (text: string, files?: File[]) => void;
  completeAssistant: (content: string, artifactId?: string) => void;
  reset: () => void;
  loadChatHistory: (chatId: string, projectId?: string) => void;

  // Artifact methods
  createArtifact: (artifact: Omit<Artifact, 'id' | 'createdAt'>) => string;
  setActiveArtifact: (id: string | null) => void;
  setArtifactPanelOpen: (open: boolean) => void;
  setArtifactPanelWidth: (width: number) => void;
  getActiveArtifact: () => Artifact | null;

  // demo helper to simulate assistant response
  simulateAssistantResponse: (
    fallback?: string,
    delayMs?: number,
    artifactId?: string
  ) => void;
}

export const useChatStore = create<ChatStore>((set, get) => ({
  messages: [],
  status: 'idle',
  artifacts: [],
  activeArtifactId: null,
  artifactPanelOpen: false,
  artifactPanelWidth: 640, // Default width
  currentChatId: null,

  startNewChat: (text: string) => {
    const trimmed = text.trim();
    if (!trimmed) return;
    set({
      messages: [
        {
          id: crypto.randomUUID(),
          role: 'user',
          content: trimmed,
          createdAt: new Date(),
        },
      ],
      status: 'thinking',
    });
  },

  sendMessage: (text: string) => {
    const trimmed = text.trim();
    if (!trimmed) return;
    const { messages } = get();
    set({
      messages: [
        ...messages,
        {
          id: crypto.randomUUID(),
          role: 'user',
          content: trimmed,
          createdAt: new Date(),
        },
      ],
      status: 'thinking',
    });
  },

  completeAssistant: (content: string, artifactId?: string) => {
    const { messages } = get();
    set({
      messages: [
        ...messages,
        {
          id: crypto.randomUUID(),
          role: 'assistant',
          content,
          createdAt: new Date(),
          artifactId,
        },
      ],
      status: 'idle',
    });
  },

  reset: () =>
    set({
      messages: [],
      status: 'idle',
      artifacts: [],
      activeArtifactId: null,
      artifactPanelOpen: false,
      artifactPanelWidth: 640,
      currentChatId: null,
    }),

  loadChatHistory: (chatId: string, projectId?: string) => {
    // In a real app, this would make an API call to load chat history
    // For demo purposes, we'll generate mock data based on the chat ID

    // General chat histories (clean numeric IDs)
    const generalChatHistories = {
      '1': [
        {
          id: crypto.randomUUID(),
          role: 'user' as ChatRole,
          content: 'Can you help me create a React product card component?',
          createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
        },
        {
          id: crypto.randomUUID(),
          role: 'assistant' as ChatRole,
          content:
            "I'd be happy to help you create a React product card component! Let me create a customizable ProductCard component for you with modern styling.",
          createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000 + 30000), // 1 day ago + 30 seconds
        },
      ],
      '2': [
        {
          id: crypto.randomUUID(),
          role: 'user' as ChatRole,
          content:
            'I need help with mobile app architecture design for a banking application.',
          createdAt: new Date(Date.now() - 48 * 60 * 60 * 1000), // 2 days ago
        },
        {
          id: crypto.randomUUID(),
          role: 'assistant' as ChatRole,
          content:
            "Excellent! Let's design a robust mobile banking app architecture. I'll help you create a secure, scalable solution with proper separation of concerns.",
          createdAt: new Date(Date.now() - 48 * 60 * 60 * 1000 + 45000), // 2 days ago + 45 seconds
        },
      ],
      '3': [
        {
          id: crypto.randomUUID(),
          role: 'user' as ChatRole,
          content:
            'How can I implement form validation with React Hook Form and Zod?',
          createdAt: new Date(Date.now() - 12 * 60 * 60 * 1000), // 12 hours ago
        },
        {
          id: crypto.randomUUID(),
          role: 'assistant' as ChatRole,
          content:
            'Great question! React Hook Form with Zod validation is a powerful combination. Let me show you how to set up robust form validation.',
          createdAt: new Date(Date.now() - 12 * 60 * 60 * 1000 + 25000), // 12 hours ago + 25 seconds
        },
      ],
    };

    // Project chat histories (clean numeric IDs within project context)
    const projectChatHistories = {
      'banking-app': {
        '1': [
          {
            id: crypto.randomUUID(),
            role: 'user' as ChatRole,
            content:
              "Let's implement biometric authentication for the banking app.",
            createdAt: new Date(Date.now() - 36 * 60 * 60 * 1000), // 1.5 days ago
          },
          {
            id: crypto.randomUUID(),
            role: 'assistant' as ChatRole,
            content:
              "Perfect! Let's implement secure biometric authentication. I'll help you set up fingerprint and face recognition for both iOS and Android.",
            createdAt: new Date(Date.now() - 36 * 60 * 60 * 1000 + 40000), // 1.5 days ago + 40 seconds
          },
        ],
        '2': [
          {
            id: crypto.randomUUID(),
            role: 'user' as ChatRole,
            content: 'How should we handle offline transaction queuing?',
            createdAt: new Date(Date.now() - 18 * 60 * 60 * 1000), // 18 hours ago
          },
          {
            id: crypto.randomUUID(),
            role: 'assistant' as ChatRole,
            content:
              'Excellent question! For offline transaction queuing, we need a robust local storage strategy with encryption and sync capabilities.',
            createdAt: new Date(Date.now() - 18 * 60 * 60 * 1000 + 35000), // 18 hours ago + 35 seconds
          },
        ],
      },
      'ecommerce-platform': {
        '1': [
          {
            id: crypto.randomUUID(),
            role: 'user' as ChatRole,
            content:
              'We need to implement a shopping cart with real-time inventory updates.',
            createdAt: new Date(Date.now() - 30 * 60 * 60 * 1000), // 30 hours ago
          },
          {
            id: crypto.randomUUID(),
            role: 'assistant' as ChatRole,
            content:
              "Great idea! Let's build a real-time shopping cart with WebSocket connections for live inventory updates and smooth user experience.",
            createdAt: new Date(Date.now() - 30 * 60 * 60 * 1000 + 28000), // 30 hours ago + 28 seconds
          },
        ],
      },
    };

    let chatHistory: ChatMsg[] = [];

    if (projectId) {
      // Load project-specific chat
      const projectChats =
        projectChatHistories[projectId as keyof typeof projectChatHistories];
      if (projectChats) {
        chatHistory = projectChats[chatId as keyof typeof projectChats] || [];
      }
    } else {
      // Load general chat
      chatHistory =
        generalChatHistories[chatId as keyof typeof generalChatHistories] || [];
    }

    set({
      messages: chatHistory,
      status: 'idle',
      currentChatId: chatId,
      // Keep artifacts and panel state for continuity
    });
  },

  // Artifact methods
  createArtifact: (artifact: Omit<Artifact, 'id' | 'createdAt'>) => {
    const id = crypto.randomUUID();
    const newArtifact: Artifact = {
      ...artifact,
      id,
      createdAt: new Date(),
    };

    set((state) => ({
      artifacts: [...state.artifacts, newArtifact],
      activeArtifactId: id,
      artifactPanelOpen: true,
    }));

    return id;
  },

  setActiveArtifact: (id: string | null) => {
    set({ activeArtifactId: id });
  },

  setArtifactPanelOpen: (open: boolean) => {
    set({ artifactPanelOpen: open });
  },

  setArtifactPanelWidth: (width: number) => {
    set({ artifactPanelWidth: width });
  },

  getActiveArtifact: () => {
    const { artifacts, activeArtifactId } = get();
    return artifacts.find((a) => a.id === activeArtifactId) || null;
  },

  simulateAssistantResponse: (
    fallback = "I'm thinking through that for you… here\u2019s a draft answer.",
    delayMs = 900,
    artifactId?: string
  ) => {
    // simple delay to show the thinking indicator
    const { completeAssistant } = get();
    set({ status: 'thinking' });
    setTimeout(() => {
      completeAssistant(fallback, artifactId);
    }, delayMs);
  },
}));
