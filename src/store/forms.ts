import { create } from 'zustand';

interface LoginFormState {
  email: string;
  password: string;
  selectedOrganization: string;
  shouldFetchOrganizations: boolean;
  isLoading: boolean;
  errors: Record<string, string>;
}

interface LoginFormActions {
  setEmail: (email: string) => void;
  setPassword: (password: string) => void;
  setSelectedOrganization: (org: string) => void;
  setShouldFetchOrganizations: (should: boolean) => void;
  setIsLoading: (loading: boolean) => void;
  setError: (field: string, error: string) => void;
  clearError: (field: string) => void;
  clearErrors: () => void;
  resetForm: () => void;
}

interface FormsStore extends LoginFormState, LoginFormActions {}

export const useFormsStore = create<FormsStore>((set) => ({
  // Login form state
  email: '',
  password: '',
  selectedOrganization: '',
  shouldFetchOrganizations: false,
  isLoading: false,
  errors: {},

  // Login form actions
  setEmail: (email: string) => set({ email }),
  setPassword: (password: string) => set({ password }),
  setSelectedOrganization: (org: string) => set({ selectedOrganization: org }),
  setShouldFetchOrganizations: (should: boolean) => set({ shouldFetchOrganizations: should }),
  setIsLoading: (loading: boolean) => set({ isLoading: loading }),
  setError: (field: string, error: string) => 
    set(state => ({ errors: { ...state.errors, [field]: error } })),
  clearError: (field: string) => 
    set(state => ({ errors: { ...state.errors, [field]: '' } })),
  clearErrors: () => set({ errors: {} }),
  resetForm: () => set({
    email: '',
    password: '',
    selectedOrganization: '',
    shouldFetchOrganizations: false,
    isLoading: false,
    errors: {}
  }),
}));