import { create } from 'zustand';

// Types for component states
interface AttachedFile {
  id: string;
  file: File;
  name: string;
  size: number;
  type: string;
}

interface ComponentStates {
  // New Chat component
  newChatInput: string;
  newChatAttachmentCount: number;

  // Chat Input component
  chatInputValue: string;
  attachedFiles: AttachedFile[];

  // Chat View component
  chatViewInput: string;
  isDesktop: boolean;

  // Chat History component
  chatHistorySearchQuery: string;
  chatHistoryVisibleCount: number;
  chatHistoryIsLoading: boolean;
  chatHistoryOpenMenuId: string | null;

  // Project Chat History component
  projectChatHistorySearchQuery: string;
  projectChatHistoryVisibleCount: number;
  projectChatHistoryIsLoading: boolean;
  projectChatHistoryOpenMenuId: string | null;

  // Projects component
  projectsSearchQuery: string;
  projectsVisibleCount: number;
  projectsIsLoading: boolean;
  projectsOpenMenuId: string | null;

  // Artifact Panel component
  artifactPanelActiveTab: 'code' | 'preview' | 'reference';
  artifactPanelIsMaximized: boolean;
  artifactPanelCopySuccess: boolean;
  artifactPanelIsResizing: boolean;
  artifactPanelMaxWidth: number;

  // Sidebar Resizer
  sidebarIsResizing: boolean;
  sidebarStartX: number;
  sidebarStartWidth: number;

  // Showcase component
  showcaseSwitchValue: boolean;
  showcaseCheckboxValue: boolean;
  showcaseIndeterminateValue: boolean;

  // Alert Dialog
  alertDialogLoading: boolean;

  // Form Field states
  formFieldHasDescription: boolean;
  formFieldHasError: boolean;

  // Page states
  tenantPageTenant: string;
}

interface ComponentActions {
  // New Chat actions
  setNewChatInput: (input: string) => void;
  setNewChatAttachmentCount: (count: number) => void;

  // Chat Input actions
  setChatInputValue: (value: string) => void;
  setAttachedFiles: (files: AttachedFile[]) => void;
  addAttachedFile: (file: AttachedFile) => void;
  removeAttachedFile: (id: string) => void;
  clearAttachedFiles: () => void;

  // Chat View actions
  setChatViewInput: (input: string) => void;
  setIsDesktop: (isDesktop: boolean) => void;

  // Chat History actions
  setChatHistorySearchQuery: (query: string) => void;
  setChatHistoryVisibleCount: (count: number) => void;
  setChatHistoryIsLoading: (loading: boolean) => void;
  setChatHistoryOpenMenuId: (id: string | null) => void;

  // Project Chat History actions
  setProjectChatHistorySearchQuery: (query: string) => void;
  setProjectChatHistoryVisibleCount: (count: number) => void;
  setProjectChatHistoryIsLoading: (loading: boolean) => void;
  setProjectChatHistoryOpenMenuId: (id: string | null) => void;

  // Projects actions
  setProjectsSearchQuery: (query: string) => void;
  setProjectsVisibleCount: (count: number) => void;
  setProjectsIsLoading: (loading: boolean) => void;
  setProjectsOpenMenuId: (id: string | null) => void;

  // Artifact Panel actions
  setArtifactPanelActiveTab: (tab: 'code' | 'preview' | 'reference') => void;
  setArtifactPanelIsMaximized: (maximized: boolean) => void;
  setArtifactPanelCopySuccess: (success: boolean) => void;
  setArtifactPanelIsResizing: (resizing: boolean) => void;
  setArtifactPanelMaxWidth: (width: number) => void;

  // Sidebar Resizer actions
  setSidebarIsResizing: (resizing: boolean) => void;
  setSidebarStartX: (x: number) => void;
  setSidebarStartWidth: (width: number) => void;

  // Showcase actions
  setShowcaseSwitchValue: (value: boolean) => void;
  setShowcaseCheckboxValue: (value: boolean) => void;
  setShowcaseIndeterminateValue: (value: boolean) => void;

  // Alert Dialog actions
  setAlertDialogLoading: (loading: boolean) => void;

  // Form Field actions
  setFormFieldHasDescription: (has: boolean) => void;
  setFormFieldHasError: (has: boolean) => void;

  // Page actions
  setTenantPageTenant: (tenant: string) => void;

  // Reset actions
  resetChatHistory: () => void;
  resetProjectChatHistory: () => void;
  resetProjects: () => void;
  resetArtifactPanel: () => void;
}

interface ComponentStore extends ComponentStates, ComponentActions {}

export const useComponentStore = create<ComponentStore>((set) => ({
  // Initial states
  newChatInput: '',
  newChatAttachmentCount: 0,
  chatInputValue: '',
  attachedFiles: [],
  chatViewInput: '',
  isDesktop: false,
  chatHistorySearchQuery: '',
  chatHistoryVisibleCount: 6,
  chatHistoryIsLoading: false,
  chatHistoryOpenMenuId: null,
  projectChatHistorySearchQuery: '',
  projectChatHistoryVisibleCount: 6,
  projectChatHistoryIsLoading: false,
  projectChatHistoryOpenMenuId: null,
  projectsSearchQuery: '',
  projectsVisibleCount: 6,
  projectsIsLoading: false,
  projectsOpenMenuId: null,
  artifactPanelActiveTab: 'code',
  artifactPanelIsMaximized: false,
  artifactPanelCopySuccess: false,
  artifactPanelIsResizing: false,
  artifactPanelMaxWidth: 960,
  sidebarIsResizing: false,
  sidebarStartX: 0,
  sidebarStartWidth: 240,
  showcaseSwitchValue: false,
  showcaseCheckboxValue: false,
  showcaseIndeterminateValue: true,
  alertDialogLoading: false,
  formFieldHasDescription: false,
  formFieldHasError: false,
  tenantPageTenant: '',

  // Actions
  setNewChatInput: (input) => set({ newChatInput: input }),
  setNewChatAttachmentCount: (count) => set({ newChatAttachmentCount: count }),
  
  setChatInputValue: (value) => set({ chatInputValue: value }),
  setAttachedFiles: (files) => set({ attachedFiles: files }),
  addAttachedFile: (file) => set(state => ({ attachedFiles: [...state.attachedFiles, file] })),
  removeAttachedFile: (id) => set(state => ({ 
    attachedFiles: state.attachedFiles.filter(f => f.id !== id) 
  })),
  clearAttachedFiles: () => set({ attachedFiles: [] }),

  setChatViewInput: (input) => set({ chatViewInput: input }),
  setIsDesktop: (isDesktop) => set({ isDesktop }),

  setChatHistorySearchQuery: (query) => set({ chatHistorySearchQuery: query }),
  setChatHistoryVisibleCount: (count) => set({ chatHistoryVisibleCount: count }),
  setChatHistoryIsLoading: (loading) => set({ chatHistoryIsLoading: loading }),
  setChatHistoryOpenMenuId: (id) => set({ chatHistoryOpenMenuId: id }),

  setProjectChatHistorySearchQuery: (query) => set({ projectChatHistorySearchQuery: query }),
  setProjectChatHistoryVisibleCount: (count) => set({ projectChatHistoryVisibleCount: count }),
  setProjectChatHistoryIsLoading: (loading) => set({ projectChatHistoryIsLoading: loading }),
  setProjectChatHistoryOpenMenuId: (id) => set({ projectChatHistoryOpenMenuId: id }),

  setProjectsSearchQuery: (query) => set({ projectsSearchQuery: query }),
  setProjectsVisibleCount: (count) => set({ projectsVisibleCount: count }),
  setProjectsIsLoading: (loading) => set({ projectsIsLoading: loading }),
  setProjectsOpenMenuId: (id) => set({ projectsOpenMenuId: id }),

  setArtifactPanelActiveTab: (tab) => set({ artifactPanelActiveTab: tab }),
  setArtifactPanelIsMaximized: (maximized) => set({ artifactPanelIsMaximized: maximized }),
  setArtifactPanelCopySuccess: (success) => set({ artifactPanelCopySuccess: success }),
  setArtifactPanelIsResizing: (resizing) => set({ artifactPanelIsResizing: resizing }),
  setArtifactPanelMaxWidth: (width) => set({ artifactPanelMaxWidth: width }),

  setSidebarIsResizing: (resizing) => set({ sidebarIsResizing: resizing }),
  setSidebarStartX: (x) => set({ sidebarStartX: x }),
  setSidebarStartWidth: (width) => set({ sidebarStartWidth: width }),

  setShowcaseSwitchValue: (value) => set({ showcaseSwitchValue: value }),
  setShowcaseCheckboxValue: (value) => set({ showcaseCheckboxValue: value }),
  setShowcaseIndeterminateValue: (value) => set({ showcaseIndeterminateValue: value }),

  setAlertDialogLoading: (loading) => set({ alertDialogLoading: loading }),

  setFormFieldHasDescription: (has) => set({ formFieldHasDescription: has }),
  setFormFieldHasError: (has) => set({ formFieldHasError: has }),

  setTenantPageTenant: (tenant) => set({ tenantPageTenant: tenant }),

  // Reset functions
  resetChatHistory: () => set({
    chatHistorySearchQuery: '',
    chatHistoryVisibleCount: 6,
    chatHistoryIsLoading: false,
    chatHistoryOpenMenuId: null,
  }),

  resetProjectChatHistory: () => set({
    projectChatHistorySearchQuery: '',
    projectChatHistoryVisibleCount: 6,
    projectChatHistoryIsLoading: false,
    projectChatHistoryOpenMenuId: null,
  }),

  resetProjects: () => set({
    projectsSearchQuery: '',
    projectsVisibleCount: 6,
    projectsIsLoading: false,
    projectsOpenMenuId: null,
  }),

  resetArtifactPanel: () => set({
    artifactPanelActiveTab: 'code',
    artifactPanelIsMaximized: false,
    artifactPanelCopySuccess: false,
    artifactPanelIsResizing: false,
    artifactPanelMaxWidth: 960,
  }),
}));