import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { decodeJwt } from 'jose';

interface JWTPayload {
  sub: string;
  email: string;
  'custom:Name'?: string;
  'custom:tenant_id': string;
  'custom:is_admin'?: string;
  'custom:is_super_admin'?: string;
  exp: number;
  iat: number;
  [key: string]: unknown;
}

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // FIRST: Handle global routes that should bypass all tenant logic
  if (pathname === '/login') {
    return NextResponse.next();
  }
  
  if (pathname === '/showcase') {
    return NextResponse.next();
  }

  // Skip middleware for static files, API routes, Next.js internals, and special pages
  if (
    pathname.startsWith('/_next') ||
    pathname.startsWith('/api') ||
    pathname.startsWith('/static') ||
    pathname.startsWith('/theme-demo') ||
    pathname === '/' ||
    pathname.includes('.') ||
    pathname === '/favicon.ico'
  ) {
    return NextResponse.next();
  }

  // Check for idToken cookie
  const idToken = request.cookies.get('idToken')?.value;
  
  if (!idToken) {
    // No token found, redirect to login
    return NextResponse.redirect(new URL('/login', request.url));
  }

  let decodedToken: JWTPayload;
  try {
    // Decode JWT token (without verification for now - in production you should verify)
    decodedToken = decodeJwt(idToken) as JWTPayload;
    
    // Check if token is expired
    const currentTime = Math.floor(Date.now() / 1000);
    if (decodedToken.exp < currentTime) {
      // Token expired, redirect to login
      return NextResponse.redirect(new URL('/login', request.url));
    }
  } catch (error) {
    // Invalid token, redirect to login
    console.error('Invalid JWT token:', error);
    return NextResponse.redirect(new URL('/login', request.url));
  }

  // Extract tenant from the URL path
  const pathSegments = pathname.split('/').filter(Boolean);
  const urlTenant = pathSegments[0];
  const jwtTenantId = decodedToken['custom:tenant_id'];

  // If no tenant in path, redirect to user's tenant
  if (!urlTenant) {
    return NextResponse.redirect(new URL(`/${jwtTenantId}/projects`, request.url));
  }

  // Validate that URL tenant matches JWT tenant_id
  if (urlTenant !== jwtTenantId) {
    // URL tenant doesn't match user's tenant, redirect to correct tenant
    const remainingPath = pathSegments.slice(1).join('/');
    const redirectPath = remainingPath ? `/${jwtTenantId}/${remainingPath}` : `/${jwtTenantId}/projects`;
    return NextResponse.redirect(new URL(redirectPath, request.url));
  }

  // Add tenant and user info to headers for use in components
  const response = NextResponse.next();
  response.headers.set('x-tenant', jwtTenantId);
  response.headers.set('x-user-id', decodedToken.sub);
  response.headers.set('x-user-email', decodedToken.email);
  if (decodedToken['custom:Name']) {
    response.headers.set('x-user-name', decodedToken['custom:Name']);
  }

  return response;
}

export const config = {
  matcher: [
    /*
     * Only match tenant-based routes (/{tenant}/...)
     * This excludes /login, /api, /_next, etc. by only matching specific patterns
     */
    '/:tenant(demo|acme|kavia|test)/:path*',
    // Also catch any other potential tenant routes that aren't in our predefined list
    '/:path((?!api|_next|favicon.ico|login|showcase)[^/]+)/:subpath*',
  ],
};
