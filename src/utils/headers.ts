import Cookies from 'js-cookie';


export const getHeaders = async () => {
  const idToken = Cookies.get("idToken");
  const is_public_selected = Cookies.get("is_public_selected");
  const selected_tenant_id = Cookies.get("selected_tenant_id");
  const selected_project_creator_email = Cookies.get(
    "selected_project_creator_email"
  );

  const headers = new Headers({
    "Content-Type": "application/json",
    Authorization: `Bearer ${idToken}`,
    is_public_selected: is_public_selected || "",
    selected_tenant_id: selected_tenant_id || "",
    selected_project_creator_email: selected_project_creator_email || "",
  });

  return headers;
};