import crypto from 'crypto';
import config from '@/lib/config';

// Constants
const TENANT_SALT: string | undefined = config.TENANT_SALT;
const ROOT_TENANT_ID: string | undefined = config.ROOT_TENANT_ID;

if (!TENANT_SALT) {
    throw new Error("TENANT_SALT is not defined or empty");
}

// Type assertion after null check
const VALIDATED_TENANT_SALT: string = TENANT_SALT;

export function encrypt(data: string, salt: string = VALIDATED_TENANT_SALT): string {
    const key = crypto.createHash('sha256').update(salt).digest().slice(0, 16); // Ensure 16-byte key
    const cipher = crypto.createCipheriv('aes-128-ecb', key, Buffer.alloc(0)); // Use an empty IV
    const encrypted = Buffer.concat([cipher.update(data, 'utf8'), cipher.final()]);
    return encrypted.toString('base64').replace(/=+$/, ''); // Base64 without padding
}

export function decrypt(data: string, salt: string = VALIDATED_TENANT_SALT): string {
    const key = crypto.createHash('sha256').update(salt).digest().slice(0, 16); // Ensure 16-byte key
    const decipher = crypto.createDecipheriv('aes-128-ecb', key, Buffer.alloc(0)); // Use an empty IV
    const decrypted = Buffer.concat([decipher.update(String(data), 'base64'), decipher.final()]);
    return decrypted.toString('utf8');
}

export function encryptTenantId(tenantId: string): string {
    const key = crypto.createHash('sha256').update(VALIDATED_TENANT_SALT).digest().slice(0, 16); // Ensure 16-byte key
    const cipher = crypto.createCipheriv('aes-128-ecb', key, Buffer.alloc(0)); // Use an empty IV
    const encrypted = Buffer.concat([cipher.update(tenantId, 'utf8'), cipher.final()]);
    return encrypted.toString('base64').replace(/=+$/, ''); // Base64 without padding
}

export function encryptString(data: string): string {
    return encrypt(data, VALIDATED_TENANT_SALT);
}

export function decryptString(data: string): string {
    return decrypt(data, VALIDATED_TENANT_SALT);
}

export const decryptStringClipboard = (value: string): string => {
    try {
        return decryptString(value);
    } catch {
        return value;
    }
};

export function decryptTenantId(encrypted: string): string | null {
    try {
        if (!encrypted) {
            throw new Error('Encrypted tenant ID is required');
        }
        const key = crypto.createHash('sha256').update(VALIDATED_TENANT_SALT).digest().slice(0, 16); // Ensure 16-byte key
        const decipher = crypto.createDecipheriv('aes-128-ecb', key, Buffer.alloc(0)); // Use an empty IV
        const decrypted = Buffer.concat([
            decipher.update(Buffer.from(encrypted, 'base64')),
            decipher.final(),
        ]);
        return decrypted.toString('utf8'); 
    } catch {
        return null;
    }
}

export function encryptedTenantId(): string {
    if (!ROOT_TENANT_ID) {
        throw new Error("ROOT_TENANT_ID is not defined");
    }
    return encryptTenantId(ROOT_TENANT_ID);
}

export function getRootTenantId(): string | undefined {
    return ROOT_TENANT_ID;
}

interface EncryptionUtils {
    encryptTenantId: (tenantId: string) => string;
    decryptTenantId: (encrypted: string) => string | null;
}

const encryptionUtils: EncryptionUtils = {
    encryptTenantId,
    decryptTenantId,
};

export default encryptionUtils;