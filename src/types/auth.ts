// Authentication-related types

export interface LoginRequest {
  email: string;
  password: string;
  organization_id?: string;
}

export interface SignupRequest {
  email: string;
  password: string;
  name?: string;
}

export interface LoginResponse {
  message: string;
  id_token: string;
  refresh_token: string;
  tenant_id: string;
  is_admin: boolean;
  is_free_user: boolean;
  is_super_admin: boolean;
  AccessToken: string;
  ExpiresIn: number;
  TokenType: string;
}

export interface SignupResponse {
  message: string;
  user_sub: string;
  tenant_id: string;
  organization_id: string;
  subscription_type: string;
}

// Organization-related types (part of auth flow)
export interface Organization {
  id: string;
  name: string;
}

export interface UserOrganizationsResponse {
  email: string;
  organizations: Organization[];
}

// Refresh Token types
export interface RefreshTokenRequest {
  refresh_token: string;
}

export interface RefreshTokenResponse {
  message: string;
  id_token: string;
} 