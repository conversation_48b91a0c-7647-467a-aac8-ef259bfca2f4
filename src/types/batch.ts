// Batch API related types

/**
 * Request model for past code tasks endpoint
 */
export interface PastCodeTasksRequest {
  /** Project ID */
  project_id: number;
  /** Number of tasks to return (1-100) */
  limit?: number;
  /** Number of tasks to skip for pagination */
  skip?: number;
  /** Filter by agent name */
  agent_name?: string;
}

export interface TaskMessage {
  content: string;
  sender: string;
  timestamp: string;
  user_id: string;
  id: string;
  status: string;
  msg_type: string;
  requires_resolution?: boolean;
  resolution_id?: string | null;
  parent_id?: string | null;
  metadata?: Record<string, unknown>;
  attachments?: unknown[];
  extra?: Record<string, unknown>;
  options?: string[];
  checkpoint_data?: {
    check_points: Array<{
      hash: string;
      message: string;
      date: string;
      author: string;
      path: string;
    }>;
  };
  task_id?: string;
  checkpoint_count?: number;
  checkpoint_number?: number;
  checkpoint_title?: string;
}

export interface MergeStatus {
  repo_name: string;
  repo_path: string;
  repo_index: number;
  merge_time: string;
  merge_status: string;
  error: string | null;
  sha: string;
  source_branch: string;
  target_branch: string;
  original_branch: string;
}

export interface MergeSummary {
  total_repositories: number;
  successful_merges: number;
  failed_merges: number;
  skipped_repositories: number;
  source_branch: string;
  target_branch: string;
  task_id: string;
}

/**
 * Individual task information in the response
 */
export interface TaskInfo {
  /** Task ID */
  _id: string;
  /** Task status */
  status?: string;
  /** LLM model used */
  llm_model?: string;
  /** Agent name */
  agent_name?: string;
  /** Task start time */
  start_time?: string;
  /** Session ID */
  session_id?: string;
  /** Task description */
  description?: string;
  /** Session name */
  session_name?: string;
  /** Container ID */
  container_id?: number;
  /** Merge status information */
  merge_status?: MergeStatus[];
  /** Merge summary */
  merge_summary?: MergeSummary;
  /** Reason for task stop */
  stop_reason?: string;
  /** Task duration (e.g., '5m 30s', '1h 20m') */
  duration?: string;
}

/**
 * Response model for past code tasks endpoint
 */
export interface PastCodeTasksResponse {
  /** List of tasks */
  tasks: TaskInfo[];
  /** Total number of tasks matching the query */
  total_count: number;
  /** Limit parameter used in the query */
  limit: number;
  /** Skip parameter used in the query */
  skip: number;
}

/**
 * Request model for past code tasks by tenant endpoint
 */
export interface PastCodeTasksByTenantRequest {
  /** Number of tasks to return (1-100) */
  limit?: number;
  /** Number of tasks to skip for pagination */
  skip?: number;
  /** Filter by agent name (e.g., 'CodeMaintenance', 'codegeneration', 'codemaintenance') */
  agent_name?: string;
}

/**
 * Enhanced task model for tenant-based queries
 */
export interface TaskModel {
  /** Task ID */
  _id: string;
  /** Task status */
  status: string;
  /** LLM model used */
  llm_model: string;
  /** Agent name */
  agent_name?: string;
  /** Task start time */
  start_time: string;
  /** Session ID */
  session_id?: string;
  /** Task description */
  description?: string;
  /** Session name */
  session_name?: string;
  /** Container ID */
  container_id?: number;
  /** Project ID */
  project_id?: number;
  /** Merge status information */
  merge_status?: MergeStatus[];
  /** Merge summary */
  merge_summary?: MergeSummary;
  /** Reason for task stop */
  stop_reason?: string;
  /** Calculated duration (e.g., "1h 30m", "<1m") */
  duration: string;
}

/**
 * Response model for past code tasks by tenant endpoint
 */
export interface PastCodeTasksByTenantResponse {
  /** List of tasks */
  tasks: TaskModel[];
  /** Total number of tasks matching the query */
  total_count: number;
  /** Limit parameter used in the query */
  limit: number;
  /** Skip parameter used in the query */
  skip: number;
  /** Tenant ID */
  tenant_id: string;
}

// Legacy alias for backward compatibility
export type CodeTask = TaskInfo;