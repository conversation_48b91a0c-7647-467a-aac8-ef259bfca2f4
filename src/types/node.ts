// Node API related types

export interface ListProjectsRequest {
  page?: number;
  page_size?: number;
  search?: string;
  creator_filter?: string;
  tenant_id?: string;
}

export interface Project {
  id: number;
  Title: string;
  created_at: string;
  created_by: string;
  creator_name: string;
  creator_email: string;
  creator_picture: string | null;
}

export interface Pagination {
  total_count: number;
  total_pages: number;
  current_page: number;
  page_size: number;
  has_next: boolean;
  has_previous: boolean;
  start_index: number;
  end_index: number;
}

export interface ListProjectsData {
  projects: Project[];
  pagination: Pagination;
}

export interface ListProjectsResponse {
  success: boolean;
  data: ListProjectsData;
}

// Project Details types
export interface ProjectProperties {
  created_by?: string | null;
  Description?: string | null;
  is_active?: boolean | null;
  status?: string | null;
  Requirement?: string | null;
  Type?: string | null;
  created_at?: string | null;
  Title?: string | null;
  updated_by?: string | null;
  updated_at?: string | null;
}

export interface ProjectDetailsResponse {
  id?: number | null;
  labels?: string[] | null;
  properties?: ProjectProperties | null;
  ui_metadata?: Record<string, unknown> | null;
}

// Request type for getting project details
export interface GetProjectDetailsRequest {
  id: number;
  node_type?: string;
}
