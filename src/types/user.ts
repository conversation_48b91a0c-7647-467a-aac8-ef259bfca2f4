// User-related types and interfaces

export interface TenantConfigurations {
  max_users: number;
  role_customization: boolean;
  api_access: boolean;
  github_integration: boolean;
  jira_integration: boolean;
  custom_reports: boolean;
  export_capabilities: boolean;
}

export interface ModelSettings {
  name: string;
  display_name: string;
  selected_model: string;
  available_models: string[];
}

export interface TenantSettings {
  showfunctioncalling: boolean;
  enable_log_download_pod_crud: boolean;
  discussion: ModelSettings;
  code_generation: ModelSettings;
  code_inspection: ModelSettings;
  conversational_chat: ModelSettings;
}

export interface TenantDetails {
  id: string;
  name: string;
  business_email: string;
  industrial_type: string;
  company_size: string;
  domain: string;
  image: string;
  plan_id: string;
  admin_id: string;
  configurations: TenantConfigurations;
  group_ids: string[];
  settings: TenantSettings;
  credits: number;
  opentopublic: boolean;
  created_at: string;
  updated_at: string;
  status: string;
}

export interface User {
  user_id: string;
  email: string;
  name: string;
  designation: string;
  department: string;
  picture: string;
  is_admin: boolean;
  tenant_id: string;
  tenant_details: TenantDetails;
  opentopublic: boolean;
  status: string;
  has_accepted_terms: boolean;
  accepted_terms_at: string | null;
  is_super_admin: boolean;
}

// API Response types
export interface UserResponse {
  user_id: string;
  email: string;
  name: string;
  designation: string;
  department: string;
  picture: string;
  is_admin: boolean;
  tenant_id: string;
  tenant_details: TenantDetails;
  opentopublic: boolean;
  status: string;
  has_accepted_terms: boolean;
  accepted_terms_at: string | null;
  is_super_admin: boolean;
}

export interface AcceptTermsResponse {
  message: string;
}

// Request types (if needed for future endpoints)
export interface UpdateUserRequest {
  name?: string;
  designation?: string;
  department?: string;
  picture?: string;
} 