import { useQuery } from '@tanstack/react-query';
import {
  PastCodeTasksRequest,
  PastCodeTasksResponse,
  PastCodeTasksByTenantRequest,
  PastCodeTasksByTenantResponse,
} from '../types/batch';
import { getHeaders } from '../utils/headers';

// API functions to call the endpoints
const batchAPI = {
  // Get past code tasks function
  getPastCodeTasks: async (params: PastCodeTasksRequest): Promise<PastCodeTasksResponse> => {
    const headers = await getHeaders();
    
    // Build query string
    const searchParams = new URLSearchParams();
    if (params.limit !== undefined) searchParams.append('limit', params.limit.toString());
    if (params.skip !== undefined) searchParams.append('skip', params.skip.toString());
    if (params.agent_name) searchParams.append('agent_name', params.agent_name);
    
    const queryString = searchParams.toString();
    const url = `/api/batch/past_code_tasks/${params.project_id}${queryString ? `?${queryString}` : ''}`;
    
    const response = await fetch(url, {
      method: 'GET',
      headers,
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `Failed to fetch past code tasks: ${response.statusText}`);
    }

    return response.json();
  },

  // Get past code tasks by tenant function
  getPastCodeTasksByTenant: async (params: PastCodeTasksByTenantRequest): Promise<PastCodeTasksByTenantResponse> => {
    const headers = await getHeaders();
    
    // Build query string
    const searchParams = new URLSearchParams();
    if (params.limit !== undefined) searchParams.append('limit', params.limit.toString());
    if (params.skip !== undefined) searchParams.append('skip', params.skip.toString());
    if (params.agent_name) searchParams.append('agent_name', params.agent_name);
    
    const queryString = searchParams.toString();
    const url = `/api/batch/past_code_tasks_by_tenant${queryString ? `?${queryString}` : ''}`;
    
    const response = await fetch(url, {
      method: 'GET',
      headers,
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `Failed to fetch past code tasks by tenant: ${response.statusText}`);
    }

    return response.json();
  },
};

// React Query hooks
export const usePastCodeTasks = (params: PastCodeTasksRequest, enabled: boolean = true) => {
  return useQuery<PastCodeTasksResponse, Error>({
    queryKey: ['past-code-tasks', params],
    queryFn: () => batchAPI.getPastCodeTasks(params),
    enabled: enabled && !!params.project_id,
    staleTime: 5 * 60 * 1000, // Consider data fresh for 5 minutes
    retry: 2,
  });
};

// Alias for consistency
export const useCodeTasks = (params: PastCodeTasksRequest, enabled: boolean = true) => {
  return usePastCodeTasks(params, enabled);
};

// Additional hook for easier usage with common parameters
export const usePastCodeTasksWithDefaults = (
  project_id: number,
  options?: {
    limit?: number;
    skip?: number;
    agent_name?: string;
    enabled?: boolean;
  }
) => {
  const params: PastCodeTasksRequest = {
    project_id,
    limit: options?.limit || 10,
    skip: options?.skip || 0,
    ...(options?.agent_name && { agent_name: options.agent_name }),
  };

  return usePastCodeTasks(params, options?.enabled !== false);
};

// React Query hook for past code tasks by tenant
export const usePastCodeTasksByTenant = (params: PastCodeTasksByTenantRequest, enabled: boolean = true) => {
  return useQuery<PastCodeTasksByTenantResponse, Error>({
    queryKey: ['past-code-tasks-by-tenant', params],
    queryFn: () => batchAPI.getPastCodeTasksByTenant(params),
    enabled: enabled,
    staleTime: 5 * 60 * 1000, // Consider data fresh for 5 minutes
    retry: 2,
  });
};

// Hook with defaults for easier usage
export const usePastCodeTasksByTenantWithDefaults = (
  options?: {
    limit?: number;
    skip?: number;
    agent_name?: string;
    enabled?: boolean;
  }
) => {
  const params: PastCodeTasksByTenantRequest = {
    limit: options?.limit || 10,
    skip: options?.skip || 0,
    ...(options?.agent_name && { agent_name: options.agent_name }),
  };

  return usePastCodeTasksByTenant(params, options?.enabled !== false);
};