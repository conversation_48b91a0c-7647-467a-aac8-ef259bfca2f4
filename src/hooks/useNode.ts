import { useQuery } from '@tanstack/react-query';
import {
  ListProjectsRequest,
  ListProjectsResponse,
  GetProjectDetailsRequest,
  ProjectDetailsResponse,
} from '../types/node';
import { getHeaders } from '../utils/headers';

// API functions to call the endpoints
const nodeAPI = {
  // List projects function
  listProjects: async (params: ListProjectsRequest): Promise<ListProjectsResponse> => {
    const headers = await getHeaders();
    
    // Build query string
    const searchParams = new URLSearchParams();
    if (params.page) searchParams.append('page', params.page.toString());
    if (params.page_size) searchParams.append('page_size', params.page_size.toString());
    if (params.search) searchParams.append('search', params.search);
    if (params.creator_filter) searchParams.append('creator_filter', params.creator_filter);
    if (params.tenant_id) searchParams.append('tenant_id', params.tenant_id);
    
    const queryString = searchParams.toString();
    const url = `/api/node/list_projects${queryString ? `?${queryString}` : ''}`;
    
    const response = await fetch(url, {
      method: 'GET',
      headers,
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `Failed to fetch projects: ${response.statusText}`);
    }

    return response.json();
  },

  // Get project details function
  getProjectDetails: async (params: GetProjectDetailsRequest): Promise<ProjectDetailsResponse> => {
    const headers = await getHeaders();
    
    // Build query string
    const searchParams = new URLSearchParams();
    if (params.node_type) searchParams.append('node_type', params.node_type);
    
    const queryString = searchParams.toString();
    const url = `/api/node/${params.id}${queryString ? `?${queryString}` : ''}`;
    
    const response = await fetch(url, {
      method: 'GET',
      headers,
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `Failed to fetch project details: ${response.statusText}`);
    }

    return response.json();
  },
};

// React Query hooks
export const useListProjects = (params: ListProjectsRequest, enabled: boolean = true) => {
  return useQuery<ListProjectsResponse, Error>({
    queryKey: ['list-projects', params],
    queryFn: () => nodeAPI.listProjects(params),
    enabled: enabled,
    staleTime: 5 * 60 * 1000, // Consider data fresh for 5 minutes
    retry: 2,
  });
};

// Alias for consistency
export const useProjects = (params: ListProjectsRequest, enabled: boolean = true) => {
  return useListProjects(params, enabled);
};

// Project Details hooks
export const useProjectDetails = (params: GetProjectDetailsRequest, enabled: boolean = true) => {
  return useQuery<ProjectDetailsResponse, Error>({
    queryKey: ['project-details', params],
    queryFn: () => nodeAPI.getProjectDetails(params),
    enabled: enabled && !!params.id,
    staleTime: 5 * 60 * 1000, // Consider data fresh for 5 minutes
    retry: 2,
  });
};

// Simplified hook for common usage - just pass project ID
export const useProject = (projectId: number, nodeType: string = 'Project', enabled: boolean = true) => {
  const params: GetProjectDetailsRequest = {
    id: projectId,
    node_type: nodeType,
  };

  return useProjectDetails(params, enabled);
};

// Alternative hook name for consistency with your example
export const useNode = (nodeId: number, nodeType: string = 'Project', enabled: boolean = true) => {
  return useProject(nodeId, nodeType, enabled);
};