import { useMutation, useQuery } from '@tanstack/react-query';
import {
  UserResponse,
  AcceptTermsResponse,
} from '../types/user';
import { getHeaders } from '../utils/headers';

// API functions to call the user endpoints
const userAPI = {
  // Accept terms and conditions
  acceptTerms: async (): Promise<AcceptTermsResponse> => {
    const headers = await getHeaders();
    
    const response = await fetch('/api/users/accept-terms', {
      method: 'POST',
      headers,
      body: '',
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `Failed to accept terms: ${response.statusText}`);
    }

    return response.json();
  },

  // Get current user details
  getUserDetails: async (): Promise<UserResponse> => {
    const headers = await getHeaders();
    
    const response = await fetch('/api/users/me/', {
      method: 'GET',
      headers,
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `Failed to fetch user details: ${response.statusText}`);
    }

    return response.json();
  },
};

// React Query hooks
export const useAcceptTerms = () => {
  return useMutation<AcceptTermsResponse, Error, void>({
    mutationFn: userAPI.acceptTerms,
    onSuccess: (data) => {
      return data;
    },
    onError: (error) => {
      console.error('Accept terms error:', error);
    },
  });
};

export const useUserDetails = (enabled: boolean = true) => {
  return useQuery<UserResponse, Error>({
    queryKey: ['user-details'],
    queryFn: userAPI.getUserDetails,
    enabled: enabled,
    staleTime: 5 * 60 * 1000, // Consider data fresh for 5 minutes
    retry: 2,
  });
};
