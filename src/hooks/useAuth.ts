import { useMutation, useQuery } from '@tanstack/react-query';
import {
  LoginRequest,
  SignupRequest,
  LoginResponse,
  SignupResponse,
  UserOrganizationsResponse,
  RefreshTokenRequest,
  RefreshTokenResponse,
} from '../types/auth';
import config from '../lib/config';

// API functions to call the endpoints
const authAPI = {
  // Login function
  login: async (credentials: LoginRequest): Promise<LoginResponse> => {
    const response = await fetch('/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(credentials),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `Login failed: ${response.statusText}`);
    }

    return response.json();
  },

  // Signup function
  signup: async (userData: SignupRequest): Promise<SignupResponse> => {
    const response = await fetch('/api/auth/signup', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(userData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `Signup failed: ${response.statusText}`);
    }

    return response.json();
  },

  // Get user organizations function
  getUserOrganizations: async (email: string): Promise<UserOrganizationsResponse> => {
    
    const response = await fetch(`/api/auth/user-organizations?email=${encodeURIComponent(email)}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `Failed to fetch user organizations: ${response.statusText}`);
    }

    return response.json();
  },

  // Google authentication function
  googleAuth: async (): Promise<void> => {
    // Redirect to the external API Google auth endpoint
    window.location.href = `${config.API_BASE_URL}/auth/google`;
  },

  // Refresh token function
  refreshToken: async (refreshTokenData: RefreshTokenRequest, tenantId?: string): Promise<RefreshTokenResponse> => {
    let url = '/api/auth/refresh_token';
    if (tenantId) {
      url += `?tenant_id=${encodeURIComponent(tenantId)}`;
    }

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(refreshTokenData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `Token refresh failed: ${response.statusText}`);
    }

    return response.json();
  },
};

// React Query hooks
export const useLogin = () => {
  
  return useMutation<LoginResponse, Error, LoginRequest>({
    mutationFn: authAPI.login,
    onSuccess: (data) => {
      return data;
    },
    onError: (error) => {
      console.error('Login error:', error);
    },
  });
};

// Hook for Google authentication
export const useGoogleAuth = () => {
  return useMutation<void, Error, void>({
    mutationFn: authAPI.googleAuth,
    onSuccess: () => {
      // The redirect happens in the googleAuth function
      console.log('Redirecting to Google authentication...');
    },
    onError: (error) => {
      console.error('Google auth error:', error);
    },
  });
};

export const useSignup = () => {
  
  return useMutation<SignupResponse, Error, SignupRequest>({
    mutationFn: authAPI.signup,
    onSuccess: (data) => {
      return data;
    },
    onError: (error) => {
      console.error('Signup error:', error);
    },
  });
};

// Hook to get user organizations
export const useUserOrganizations = (email: string, enabled: boolean = true) => {
  return useQuery<UserOrganizationsResponse, Error>({
    queryKey: ['user-organizations', email],
    queryFn: () => authAPI.getUserOrganizations(email),
    enabled: enabled && !!email, // Only run query if email is provided and enabled is true
    staleTime: 5 * 60 * 1000, // Consider data fresh for 5 minutes
    retry: 2,
  });
};

// Hook to refresh token
export const useRefreshToken = () => {
  return useMutation<RefreshTokenResponse, Error, { refreshTokenData: RefreshTokenRequest; tenantId?: string }>({
    mutationFn: ({ refreshTokenData, tenantId }) => authAPI.refreshToken(refreshTokenData, tenantId),
    onSuccess: (data) => {
      console.log('Token refreshed successfully:', data);
      return data;
    },
    onError: (error) => {
      console.error('Token refresh error:', error);
    },
  });
};
