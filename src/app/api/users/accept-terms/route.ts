import { NextRequest, NextResponse } from 'next/server';
import config from '@/lib/config';

const API_BASE_URL = config.API_BASE_URL;

export async function POST(request: NextRequest) {
  try {
    // Get authorization header from the request
    const authHeader = request.headers.get('Authorization');
    
    if (!authHeader) {
      return NextResponse.json(
        { error: 'Authorization header is required' },
        { status: 401 }
      );
    }

    // Forward the request to your backend API with all headers
    const response = await fetch(`${API_BASE_URL}/users/accept-terms`, {
      method: 'POST',
      headers: {
        'Authorization': authHeader,
        'Content-Type': 'application/json',
        'accept': 'application/json',
        // Forward other custom headers if they exist
        'is_public_selected': request.headers.get('is_public_selected') || '',
        'selected_tenant_id': request.headers.get('selected_tenant_id') || '',
        'selected_project_creator_email': request.headers.get('selected_project_creator_email') || '',
      },
      body: '', // Empty body as per your curl example
    });

    const data = await response.json();

    // Return the response from your backend
    return NextResponse.json(data, { 
      status: response.status 
    });
  } catch (error) {
    console.error('Accept Terms API Error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 