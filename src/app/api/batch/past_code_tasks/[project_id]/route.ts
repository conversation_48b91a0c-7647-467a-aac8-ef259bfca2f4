import { NextRequest, NextResponse } from 'next/server';
import config from '@/lib/config';

const API_BASE_URL = config.API_BASE_URL;

export async function GET(
  request: NextRequest,
  { params }: { params: { project_id: string } }
) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = searchParams.get('limit');
    const skip = searchParams.get('skip');
    const agent_name = searchParams.get('agent_name');
    const { project_id } = await params;
    
    // Build the URL with path parameter and query parameters
    let url = `${API_BASE_URL}/batch/past_code_tasks/${project_id}`;
    const queryParams = new URLSearchParams();
    
    if (limit) queryParams.append('limit', limit);
    if (skip) queryParams.append('skip', skip);
    if (agent_name) queryParams.append('agent_name', agent_name);
    
    if (queryParams.toString()) {
      url += `?${queryParams.toString()}`;
    }
    
    // Get authorization header from the request
    const authHeader = request.headers.get('Authorization');
    
    if (!authHeader) {
      return NextResponse.json(
        { error: 'Authorization header is required' },
        { status: 401 }
      );
    }
    
    // Forward the request to your backend API
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'accept': 'application/json',
        'Authorization': authHeader,
        'Content-Type': 'application/json',
        // Forward other custom headers if they exist
        'is_public_selected': request.headers.get('is_public_selected') || '',
        'selected_tenant_id': request.headers.get('selected_tenant_id') || '',
        'selected_project_creator_email': request.headers.get('selected_project_creator_email') || '',
      },
    });

    const data = await response.json();

    // Return the response from your backend
    return NextResponse.json(data, { 
      status: response.status 
    });
  } catch (error) {
    console.error('Past Code Tasks API Error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 