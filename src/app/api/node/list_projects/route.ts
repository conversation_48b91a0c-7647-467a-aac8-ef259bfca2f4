import { NextRequest, NextResponse } from 'next/server';
import config from '@/lib/config';

const API_BASE_URL = config.API_BASE_URL;

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = searchParams.get('page');
    const page_size = searchParams.get('page_size');
    const search = searchParams.get('search');
    const creator_filter = searchParams.get('creator_filter');
    const tenant_id = searchParams.get('tenant_id');
    
    // Build the URL with query parameters
    let url = `${API_BASE_URL}/node/list_projects/`;
    const params = new URLSearchParams();
    
    if (page) params.append('page', page);
    if (page_size) params.append('page_size', page_size);
    if (search) params.append('search', search);
    if (creator_filter) params.append('creator_filter', creator_filter);
    if (tenant_id) params.append('tenant_id', tenant_id);
    
    if (params.toString()) {
      url += `?${params.toString()}`;
    }
    
    // Get authorization header from the request
    const authHeader = request.headers.get('Authorization');
    
    if (!authHeader) {
      return NextResponse.json(
        { error: 'Authorization header is required' },
        { status: 401 }
      );
    }
    
    // Forward the request to your backend API
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'accept': 'application/json',
        'Authorization': authHeader,
        'Content-Type': 'application/json',
        // Forward other custom headers if they exist
        'is_public_selected': request.headers.get('is_public_selected') || '',
        'selected_tenant_id': request.headers.get('selected_tenant_id') || '',
        'selected_project_creator_email': request.headers.get('selected_project_creator_email') || '',
      },
    });

    const data = await response.json();

    // Return the response from your backend
    return NextResponse.json(data, { 
      status: response.status 
    });
  } catch (error) {
    console.error('List Projects API Error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 