import { NextRequest, NextResponse } from 'next/server';
import config from '@/lib/config';

const API_BASE_URL = config.API_BASE_URL;

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Validate that we have an ID parameter
    if (!(await params).id) {
      return NextResponse.json(
        {
          success: false,
          message: 'Missing node ID parameter',
          error: { message: 'Node ID is required' }
        },
        { status: 400 }
      );
    }

    const { searchParams } = new URL(request.url);
    const node_type = searchParams.get('node_type') || 'Project';
    const { id } = await params;
    
    // Build the URL with path parameter and query parameters
    let url = `${API_BASE_URL}/node/${id}`;
    const queryParams = new URLSearchParams();
    
    if (node_type) queryParams.append('node_type', node_type);
    
    if (queryParams.toString()) {
      url += `?${queryParams.toString()}`;
    }
    
    // Get authorization header from the request
    const authHeader = request.headers.get('Authorization');
    
    if (!authHeader) {
      return NextResponse.json(
        { error: 'Authorization header is required' },
        { status: 401 }
      );
    }
    
    console.log('Fetching node details from:', url);
    
    // Forward the request to your backend API
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'accept': 'application/json',
        'Authorization': authHeader,
        'Content-Type': 'application/json',
        // Forward other custom headers if they exist
        'is_public_selected': request.headers.get('is_public_selected') || '',
        'selected_tenant_id': request.headers.get('selected_tenant_id') || '',
        'selected_project_creator_email': request.headers.get('selected_project_creator_email') || '',
      },
    });

    const data = await response.json();

    // Return the response from your backend
    return NextResponse.json(data, {
      status: response.status
    });
  } catch (error) {
    console.error('Node Details API Error:', error);
    console.error('Request URL:', request.url);
    console.error('Params:', params);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}