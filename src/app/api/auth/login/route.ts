import { NextRequest, NextResponse } from 'next/server';
import config from '@/lib/config';

const API_BASE_URL = config.API_BASE_URL;

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Forward the request to your backend API
    const response = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    const data = await response.json();

    // Return the response from your backend
    return NextResponse.json(data, { 
      status: response.status,
      headers: {
        'Set-Cookie': response.headers.get('Set-Cookie') || '',
      }
    });
  } catch (error) {
    console.error('Login API Error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 