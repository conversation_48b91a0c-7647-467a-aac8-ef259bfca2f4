import { NextRequest, NextResponse } from 'next/server';
import config from '@/lib/config';

const API_BASE_URL = config.API_BASE_URL;

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { searchParams } = new URL(request.url);
    const tenant_id = searchParams.get('tenant_id');
    
    // Build the URL with query parameters
    let url = `${API_BASE_URL}/auth/refresh_token`;
    if (tenant_id) {
      url += `?tenant_id=${encodeURIComponent(tenant_id)}`;
    }
    
    // Forward the request to your backend API
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'accept': 'application/json',
        // Forward authorization header if present
        'Authorization': request.headers.get('Authorization') || '',
      },
      body: JSON.stringify(body),
    });

    const data = await response.json();

    // Return the response from your backend
    return NextResponse.json(data, { 
      status: response.status,
      headers: {
        // Forward any Set-Cookie headers for token management
        'Set-Cookie': response.headers.get('Set-Cookie') || '',
      }
    });
  } catch (error) {
    console.error('Refresh Token API Error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 