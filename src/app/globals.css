@import 'tailwindcss';

/* ==============================
   Kavia AI Theme Variables
   ============================== */
:root {
  /* Light Theme */
  --background: 0 0% 100%;
  --foreground: 0 0% 9%;
  --card: 0 0% 100%;
  --card-foreground: 0 0% 9%;
  --popover: 0 0% 100%;
  --popover-foreground: 0 0% 9%;

  /* Kavia Brand Orange */
  --primary: 22 100% 50%;
  --primary-foreground: 0 0% 100%;

  /* Secondary Colors */
  --secondary: 0 0% 96%;
  --secondary-foreground: 0 0% 9%;
  --muted: 0 0% 96%;
  --muted-foreground: 0 0% 45%;
  --accent: 0 0% 94%;
  --accent-foreground: 0 0% 9%;

  /* Status Colors */
  --destructive: 0 84% 60%;
  --destructive-foreground: 0 0% 98%;
  --success: 142 71% 45%;
  --success-foreground: 0 0% 100%;
  --warning: 38 92% 50%;
  --warning-foreground: 0 0% 100%;

  /* Borders & Inputs */
  --border: 0 0% 90%;
  --input: 0 0% 90%;
  --ring: 22 100% 50%;

  /* Radius */
  --radius: 0.5rem;
}

/* ==============================
   Dark Theme
   ============================== */
.dark {
  /* Dark Theme - Based on Kavia.ai website */
  --background: 15 9% 12%;
  --foreground: 0 0% 98%;
  --card: 15 9% 12%;
  --card-foreground: 0 0% 98%;
  --popover: 15 9% 15%;
  --popover-foreground: 0 0% 98%;

  /* Kavia Brand Orange - Brighter for dark theme */
  --primary: 22 100% 55%;
  --primary-foreground: 15 9% 12%;

  /* Secondary Colors */
  --secondary: 15 9% 18%;
  --secondary-foreground: 0 0% 98%;
  --muted: 15 9% 18%;
  --muted-foreground: 0 0% 65%;
  --accent: 15 9% 20%;
  --accent-foreground: 0 0% 98%;

  /* Status Colors */
  --destructive: 0 84% 65%;
  --destructive-foreground: 0 0% 98%;
  --success: 142 71% 50%;
  --success-foreground: 15 9% 12%;
  --warning: 38 92% 55%;
  --warning-foreground: 15 9% 12%;

  /* Borders & Inputs */
  --border: 15 9% 20%;
  --input: 15 9% 20%;
  --ring: 22 100% 55%;
}

/* ==============================
   Base Styles
   ============================== */
html {
  color-scheme: light;
  height: 100%;
  overflow: hidden;
}

html.dark {
  color-scheme: dark;
}

* {
  border-color: hsl(var(--border));
}

body {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  font-feature-settings: 'rlig' 1, 'calt' 1;
  height: 100%;
  overflow: hidden;
}

/* ==============================
   Scrollbar & Selection Styles
   ============================== */

/* Scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--border));
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.4);
}

/* Selection */
::selection {
  background: hsl(var(--primary) / 0.2);
  color: hsl(var(--foreground));
}

/* Focus Styles */
.focus-visible {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}
