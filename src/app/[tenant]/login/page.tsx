interface LoginPageProps {
  params: Promise<{
    tenant: string;
  }>;
}

import { ThemeToggle } from '@/components/theme/theme-toggle';

export default async function LoginPage({ params }: LoginPageProps) {
  const { tenant } = await params;
  return (
    <div className="bg-background relative flex min-h-screen items-center justify-center">
      <div className="absolute top-4 right-4">
        <ThemeToggle />
      </div>
      <div className="w-full max-w-md p-6">
        <div className="mb-8 text-center">
          <h1 className="text-foreground text-2xl font-bold">
            Welcome to {tenant}
          </h1>
          <p className="text-muted-foreground mt-2">
            Sign in to access your Kavia chatbot
          </p>
        </div>

        <div className="border-border/60 bg-card/60 rounded-xl border p-6">
          <form className="space-y-4">
            <div>
              <label
                htmlFor="email"
                className="text-foreground mb-2 block text-sm font-medium"
              >
                Email
              </label>
              <input
                type="email"
                id="email"
                className="text-foreground placeholder:text-muted-foreground border-input bg-background w-full rounded-lg border px-3 py-2"
                placeholder="Enter your email"
              />
            </div>

            <div>
              <label
                htmlFor="password"
                className="text-foreground mb-2 block text-sm font-medium"
              >
                Password
              </label>
              <input
                type="password"
                id="password"
                className="text-foreground placeholder:text-muted-foreground border-input bg-background w-full rounded-lg border px-3 py-2"
                placeholder="Enter your password"
              />
            </div>

            <button
              type="submit"
              className="border-primary/20 bg-primary/10 text-primary hover:bg-primary/20 w-full rounded-md border px-4 py-2"
            >
              Sign In
            </button>
          </form>
        </div>
      </div>
    </div>
  );
}
