'use client';

import { useParams } from 'next/navigation';
import { useEffect } from 'react';
import ChatView from '@/components/content/chat-view';
import { useChatStore } from '@/store/chat';



export default function ChatPage() {
  const resolvedParams = useParams<{ tenant: string; chatId: string }>();
  const tenant = resolvedParams?.tenant as string;
  const chatId = resolvedParams?.chatId as string;
  const loadChatHistory = useChatStore((s) => s.loadChatHistory);
  const reset = useChatStore((s) => s.reset);

  useEffect(() => {
    // Reset the chat store first
    reset();

    // Load the chat history for this specific chat
    if (chatId) {
      loadChatHistory(chatId);
    }
  }, [chatId, loadChatHistory, reset]);

  if (!tenant || !chatId) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="mb-2 text-lg font-medium text-gray-900">
            Loading chat...
          </div>
          <div className="text-sm text-gray-500">
            Please wait while we load your conversation
          </div>
        </div>
      </div>
    );
  }

  return <ChatView chatId={chatId} />;
}
