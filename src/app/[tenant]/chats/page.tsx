'use client';

import { useRouter } from 'next/navigation';
import { useEffect, useMemo } from 'react';
import { ChatHistory } from '@/components/content/chat-history';
import { useComponentStore } from '@/store/components';
import { usePastCodeTasksByTenantWithDefaults } from '@/hooks/useBatch';
import { MessageSquare } from 'lucide-react';

interface ChatsPageProps {
  params: Promise<{
    tenant: string;
  }>;
}

export default function ChatsPage({ params }: ChatsPageProps) {
  const router = useRouter();
  const { tenantPageTenant: tenant, setTenantPageTenant: setTenant } = useComponentStore();

  // Always call hooks at the top level - never conditionally
  const {
    data: pastTasksData,
    isLoading: tasksLoading,
    error: tasksError
  } = usePastCodeTasksByTenantWithDefaults({
    limit: 50,
    skip: 0
  });

  useEffect(() => {
    params.then((p) => {
      setTenant(p.tenant);
    });
  }, [params, setTenant]);

  // Helper functions for data transformation - moved to top level
  const getRelativeTime = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));
    
    if (diffInDays === 0) return 'Today';
    if (diffInDays === 1) return 'Yesterday';
    if (diffInDays < 30) return `${diffInDays} days ago`;
    if (diffInDays < 365) return `${Math.floor(diffInDays / 30)} months ago`;
    return `${Math.floor(diffInDays / 365)} years ago`;
  };

  const getTaskIcon = () => {
    return <MessageSquare className="h-5 w-5" />;
  };

  // Transform TaskModel data to ChatItem format - moved to top level before any early returns
  const transformedChats = useMemo(() => {
    if (!pastTasksData?.tasks) return [];
    
    return pastTasksData.tasks.map((task) => ({
      id: task._id,
      title: task.session_name || task.description || `Task ${task._id}`,
      description: task.description || 'No description available',
      timestamp: task.start_time ? getRelativeTime(task.start_time) : 'Unknown',
      emoji: getTaskIcon(),
      createdAt: task.start_time ? new Date(task.start_time) : new Date(),
      messageCount: 0, // Messages field removed from API response
    }));
  }, [pastTasksData]);

  const handleChatClick = (chatId: string) => {
    // Navigate to specific chat interface
    if (tenant) {
      router.push(`/${tenant}/chats/${chatId}`);
    }
  };

  const handleNewChat = () => {
    // Navigate to new chat creation
    if (tenant) {
      router.push(`/${tenant}/newchat`);
    }
  };

  if (!tenant) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        Loading...
      </div>
    );
  }

  return (
    <ChatHistory
      onChatClick={handleChatClick}
      onNewChat={handleNewChat}
      chats={transformedChats}
      isLoading={tasksLoading}
      error={tasksError}
    />
  );
}
