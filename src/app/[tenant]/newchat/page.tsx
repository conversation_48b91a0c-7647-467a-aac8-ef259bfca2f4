'use client';

import { useEffect } from 'react';
import { NewChat } from '@/components/content/new-chat';
import { useComponentStore } from '@/store/components';

interface NewChatPageProps {
  params: Promise<{
    tenant: string;
  }>;
}

export default function NewChatPage({ params }: NewChatPageProps) {
  const { tenantPageTenant: tenant, setTenantPageTenant: setTenant } = useComponentStore();

  useEffect(() => {
    params.then((p) => {
      setTenant(p.tenant);
    });
  }, [params, setTenant]);

  if (!tenant) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        Loading...
      </div>
    );
  }

  return <NewChat />;
}
