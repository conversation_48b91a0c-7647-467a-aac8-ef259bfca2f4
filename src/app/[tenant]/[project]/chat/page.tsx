'use client';

import { useRouter, useParams } from 'next/navigation';
import { ProjectChatHistory } from '@/components/content/project-chat-history';
import { usePastCodeTasks } from '@/hooks/useBatch';
import { useProject } from '@/hooks/useNode';
import {
  Bot,
  Zap,
  Brain,
  Music,
  Gem,
  MessageSquare,
  Wrench,
  FileText,
  Palette,
  Bug,
  Sparkles,
  Rocket,
  Lightbulb,
  BarChart3,
  Search
} from 'lucide-react';

export default function ChatPage() {
  const router = useRouter();
  const params = useParams<{ tenant: string; project: string }>();
  const tenant = (params?.tenant as string) || '';
  const projectId = (params?.project as string) || '';
  
  // Extract numeric project ID from string (e.g., 'project-1' -> 1)
  const numericProjectId = projectId ? parseInt(projectId.replace('project-', '')) : 0;
  
  // Fetch past code tasks for this project
  const {
    data: pastTasks,
    isLoading: tasksLoading,
    error: tasksError
  } = usePastCodeTasks({
    project_id: numericProjectId,
    limit: 20,
    skip: 0
  }, !!numericProjectId); // Only fetch if we have a valid project ID

  // Fetch project details
  const {
    data: projectDetails,
    isLoading: projectLoading,
    error: projectError
  } = useProject(numericProjectId, 'Project', !!numericProjectId);

  // Helper functions for data transformation
  const getRelativeTime = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));
    
    if (diffInDays === 0) return 'Today';
    if (diffInDays === 1) return 'Yesterday';
    if (diffInDays < 30) return `${diffInDays} days ago`;
    if (diffInDays < 365) return `${Math.floor(diffInDays / 30)} months ago`;
    return `${Math.floor(diffInDays / 365)} years ago`;
  };

  const getTaskIcon = (agentName?: string, index?: number) => {
    const agentIcons: { [key: string]: React.ComponentType<{ className?: string }> } = {
      'gpt-4': Bot,
      'gpt-4-turbo': Zap,
      'claude-3': Brain,
      'claude-sonnet': Music,
      'gemini': Gem,
    };
    
    if (agentName && agentIcons[agentName]) {
      const IconComponent = agentIcons[agentName];
      return <IconComponent className="h-5 w-5" />;
    }
    
    // Fallback icons
    const fallbackIcons = [MessageSquare, Wrench, FileText, Palette, Bug, Sparkles, Rocket, Lightbulb, BarChart3, Search];
    const IconComponent = fallbackIcons[(index || 0) % fallbackIcons.length];
    return <IconComponent className="h-5 w-5" />;
  };

  // Transform TaskInfo data to ProjectChatItem format
  const transformedTasks = pastTasks?.tasks.map((task, index) => ({
    id: task._id,
    title: task.session_name || task.description || `Task ${task._id}`,
    description: task.description || 'No description available',
    timestamp: task.start_time ? getRelativeTime(task.start_time) : 'Unknown',
    emoji: getTaskIcon(task.agent_name, index),
    createdAt: task.start_time ? new Date(task.start_time) : new Date(),
    messageCount: 0, // Messages field removed from API response
  })) || [];

  const handleChatClick = (chatId: string) => {
    // Navigate to specific project chat interface
    if (tenant && projectId) {
      router.push(
        `/${tenant}/projects/${numericProjectId}/chats/${chatId}`
      );
    }
  };

  const handleNewChat = () => {
    // Navigate to new chat creation or main chat interface for this project
    // For now, we'll check if there's a newchat route or navigate to the general chat interface
    console.log('Create new chat for project:', projectId);
    // In production, this might navigate to a new chat interface:
    // router.push(`/${tenant}/${projectId}/newchat`);
  };

  const handleBackToProjects = () => router.push(`/${tenant}/projects`);

  if (!projectId || !tenant) return null;

  // Use real project data from API or fallback when no data
  const projectData = {
    name: projectDetails?.properties?.Title || `Project ${numericProjectId}`,
    description: projectDetails?.properties?.Description || projectDetails?.properties?.Requirement || `Project ${numericProjectId}`,
    techStack: projectDetails?.properties?.Type ? [projectDetails.properties.Type] : [],
  };

  return (
    <ProjectChatHistory
      projectName={projectData.name}
      projectDescription={projectData.description}
      projectTechStack={projectData.techStack}
      pastTasks={transformedTasks}
      isTasksLoading={tasksLoading}
      isProjectLoading={projectLoading}
      tasksError={tasksError || projectError}
      onChatClick={handleChatClick}
      onNewChat={handleNewChat}
      onBackToProjects={handleBackToProjects}
    />
  );
}
