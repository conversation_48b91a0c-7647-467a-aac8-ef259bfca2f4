'use client';

import { createContext, useContext, ReactNode } from 'react';
import { Tenant } from '@/types';

interface TenantContextValue {
  tenant: Tenant | null;
  tenantSlug: string;
}

const TenantContext = createContext<TenantContextValue | undefined>(undefined);

interface TenantProviderProps {
  children: ReactNode;
  tenantSlug: string;
  tenant?: Tenant | null;
}

export function TenantProvider({
  children,
  tenantSlug,
  tenant = null,
}: TenantProviderProps) {
  const value: TenantContextValue = {
    tenant,
    tenantSlug,
  };

  return (
    <TenantContext.Provider value={value}>{children}</TenantContext.Provider>
  );
}

export function useTenant() {
  const context = useContext(TenantContext);
  if (context === undefined) {
    throw new Error('useTenant must be used within a TenantProvider');
  }
  return context;
}

// Hook to get tenant-aware API URLs
export function useTenantAPI() {
  const { tenantSlug } = useTenant();

  return {
    getURL: (path: string) => `/api/${tenantSlug}${path}`,
    tenant: tenantSlug,
  };
}
