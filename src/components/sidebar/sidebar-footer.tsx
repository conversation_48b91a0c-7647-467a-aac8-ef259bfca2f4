'use client';

import React from 'react';
import Link from 'next/link';
import { Settings, User, LogOut, HelpCircle, Keyboard } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { ThemeToggle } from '@/components/theme/theme-toggle';
import { useTenant } from '@/components/tenant/tenant-provider';
import { cn } from '@/lib/utils';

interface SidebarFooterProps {
  className?: string;
  showUserProfile?: boolean;
  showThemeToggle?: boolean;
}

export function SidebarFooter({
  className,
  showUserProfile = true,
  showThemeToggle = true,
}: SidebarFooterProps) {
  const { tenantSlug } = useTenant();

  // Mock user data - replace with actual user context
  const user = {
    name: '<PERSON> Doe',
    email: '<EMAIL>',
    avatar: '',
    initials: 'JD',
  };

  const handleLogout = () => {
    // TODO: Implement logout functionality
    console.log('Logout');
  };

  const handleKeyboardShortcuts = () => {
    // TODO: Implement keyboard shortcuts modal
    console.log('Show keyboard shortcuts');
  };

  return (
    <div
      className={cn(
        'border-border flex items-center justify-between border-t p-4',
        'bg-background/95 backdrop-blur-sm',
        className
      )}
    >
      {/* User Profile */}
      {showUserProfile && (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              className="flex h-auto w-full items-center justify-start space-x-2 p-2"
            >
              <Avatar className="h-6 w-6">
                <AvatarImage src={user.avatar} alt={user.name} />
                <AvatarFallback className="text-xs">
                  {user.initials}
                </AvatarFallback>
              </Avatar>
              <div className="flex min-w-0 flex-1 flex-col items-start">
                <span className="truncate text-sm font-medium">
                  {user.name}
                </span>
                <span className="text-muted-foreground truncate text-xs">
                  {user.email}
                </span>
              </div>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className="w-56">
            <DropdownMenuLabel>My Account</DropdownMenuLabel>
            <DropdownMenuSeparator />

            <DropdownMenuItem asChild>
              <Link href={`/${tenantSlug}/profile`}>
                <User className="mr-2 h-4 w-4" />
                Profile
              </Link>
            </DropdownMenuItem>

            <DropdownMenuItem asChild>
              <Link href={`/${tenantSlug}/settings`}>
                <Settings className="mr-2 h-4 w-4" />
                Settings
              </Link>
            </DropdownMenuItem>

            <DropdownMenuSeparator />

            <DropdownMenuItem onClick={handleKeyboardShortcuts}>
              <Keyboard className="mr-2 h-4 w-4" />
              Keyboard shortcuts
            </DropdownMenuItem>

            <DropdownMenuItem asChild>
              <Link href="/help">
                <HelpCircle className="mr-2 h-4 w-4" />
                Help & Support
              </Link>
            </DropdownMenuItem>

            <DropdownMenuSeparator />

            <DropdownMenuItem
              onClick={handleLogout}
              className="text-destructive focus:text-destructive"
            >
              <LogOut className="mr-2 h-4 w-4" />
              Sign out
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )}

      {/* Controls */}
      <div className="flex items-center space-x-1">
        {/* Theme Toggle */}
        {showThemeToggle && <ThemeToggle />}

        {/* Help */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Button variant="ghost" asChild className="h-8 w-8 p-0">
              <Link href="/help">
                <HelpCircle className="h-4 w-4" />
              </Link>
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Help & Support</p>
          </TooltipContent>
        </Tooltip>
      </div>
    </div>
  );
}

export default SidebarFooter;
