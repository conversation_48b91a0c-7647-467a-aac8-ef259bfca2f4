'use client';

import React, { useMemo, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Plus,
  MessageSquare,
  Briefcase,
  PanelLeftOpen,
  PanelLeftClose,
  User,
  LogOut,
  Settings,
  MoreHorizontal,
  Trash2,
  Edit3,
} from 'lucide-react';
// import { Button } from '@/components/ui/button';
import { IconButton } from '@/components/ui/icon-button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Sheet, SheetContent, SheetTitle } from '@/components/ui/sheet';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { useSidebar } from './sidebar-context';
import { useTenant } from '@/components/tenant/tenant-provider';
import { SimpleThemeToggle } from '@/components/theme/theme-toggle';
import { cn } from '@/lib/utils';

interface UnifiedSidebarProps {
  className?: string;
}

export function UnifiedSidebar({ className }: UnifiedSidebarProps) {
  const { state, toggle, setActiveItem, closeMobileMenu } = useSidebar();
  const { tenantSlug } = useTenant();
  const router = useRouter();

  // Mock user data
  const user = {
    name: 'John Doe',
    email: '<EMAIL>',
    avatar: '',
    initials: 'JD',
  };

  // Mock chat history data
  const recentChats = [
    { id: '1', title: 'AI Code Review Assistant', time: '2 hours ago' },
    { id: '2', title: 'React Component Design', time: '1 day ago' },
    { id: '3', title: 'Database Schema Planning', time: '2 days ago' },
    { id: '4', title: 'API Integration Help', time: '3 days ago' },
    { id: '5', title: 'Performance Optimization', time: '1 week ago' },
  ];

  const handleLogout = useCallback(() => {
    console.log('Logout');
  }, []);

  const handleItemClick = useCallback(
    (itemId: string) => {
      setActiveItem(itemId);

      // Close mobile menu when navigating
      if (state.isMobile && state.isMobileMenuOpen) {
        closeMobileMenu();
      }

      switch (itemId) {
        case 'projects':
          router.push(`/${tenantSlug}/projects`);
          break;
        case 'chat-history':
          router.push(`/${tenantSlug}/chats`);
          break;
        case 'new-chat':
          router.push(`/${tenantSlug}/newchat`);
          break;
        default:
          break;
      }
    },
    [
      setActiveItem,
      router,
      tenantSlug,
      state.isMobile,
      state.isMobileMenuOpen,
      closeMobileMenu,
    ]
  );

  const handleChatAction = useCallback((action: string, chatId: string) => {
    console.log(`${action} chat:`, chatId);
  }, []);

  const navItems = useMemo(
    () => [
      {
        id: 'new-chat',
        label: 'New Chat',
        icon: Plus,
        href: `/${tenantSlug}/project-1/chat`,
        isActive: state.activeItem === 'new-chat',
        onClick: () => handleItemClick('new-chat'),
        hasAction: true,
      },
      {
        id: 'chat-history',
        label: 'Chats',
        icon: MessageSquare,
        href: `/${tenantSlug}/project-1/chat`,
        isActive: state.activeItem === 'chat-history',
        onClick: () => handleItemClick('chat-history'),
      },
      {
        id: 'projects',
        label: 'Projects',
        icon: Briefcase,
        href: `/${tenantSlug}/projects`,
        isActive: state.activeItem === 'projects',
        onClick: () => handleItemClick('projects'),
      },
    ],
    [tenantSlug, state.activeItem, handleItemClick]
  );

  // Desktop sidebar content component
  const DesktopSidebarContent = () => (
    <div className="flex flex-col h-full">
      {/* Header - Toggle icon on left, KAVIA text in center, theme toggle on right when expanded */}
      <div className="flex items-center h-16 p-4">
        {/* Toggle button - Always on the left, fixed size */}
        <div className="h-8 w-8 flex-shrink-0">
          <Tooltip>
            <TooltipTrigger asChild>
              <IconButton
                variant="ghost"
                onClick={toggle}
                className="h-8 w-8 rounded-xl hover:bg-[hsl(var(--primary))]/10"
                aria-label={
                  state.isExpanded ? 'Collapse Sidebar' : 'Expand Sidebar'
                }
                icon={
                  state.isExpanded ? (
                    <PanelLeftClose className="h-4 w-4 text-[hsl(var(--primary))]" />
                  ) : (
                    <PanelLeftOpen className="h-4 w-4 text-[hsl(var(--primary))]" />
                  )
                }
              />
            </TooltipTrigger>
            <TooltipContent side="right">
              <p>{state.isExpanded ? 'Collapse Sidebar' : 'Expand Sidebar'}</p>
            </TooltipContent>
          </Tooltip>
        </div>
        
        {/* KAVIA text and theme toggle - Only when expanded */}
        <AnimatePresence>
          {state.isExpanded && (
            <motion.div
              initial={{ opacity: 0, width: 0 }}
              animate={{ opacity: 1, width: 'auto' }}
              exit={{ opacity: 0, width: 0 }}
              transition={{ duration: 0.2, ease: 'easeInOut' }}
              className="flex items-center justify-between flex-1 ml-3 overflow-hidden"
            >
              <h1 className="text-sm font-semibold text-foreground whitespace-nowrap">KAVIA</h1>
              <div className="flex-shrink-0 ml-2">
                <SimpleThemeToggle />
              </div>
            </motion.div>
          )}
        </AnimatePresence>
        
        {/* Theme toggle when collapsed - show as tooltip */}
        {!state.isExpanded && (
          <div className="flex-shrink-0 w-8 h-8 ml-2">
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="w-8 h-8">
                  <SimpleThemeToggle />
                </div>
              </TooltipTrigger>
              <TooltipContent side="right">
                <p>Toggle Theme</p>
              </TooltipContent>
            </Tooltip>
          </div>
        )}
      </div>

      {/* Navigation Menu - Original Style with Brand Colors */}
      <div className="space-y-1 px-3 py-2">
        {/* New Chat - Original style with action button */}
        <Tooltip>
          <TooltipTrigger asChild>
            <div
              className={cn(
                'flex items-center transition-all duration-200 rounded-lg cursor-pointer group relative',
                'hover:bg-[hsl(var(--primary))]/10 p-3',
                navItems[0].isActive 
                  ? 'bg-[hsl(var(--primary))]/10 text-[hsl(var(--primary))]' 
                  : 'text-muted-foreground hover:text-[hsl(var(--primary))]'
              )}
              onClick={() => handleItemClick('new-chat')}
            >
              {/* Icon - Always visible and fixed position */}
              <div className="flex h-4 w-4 flex-shrink-0 items-center justify-center">
                <Plus className="h-4 w-4" />
              </div>

              {/* Text and Action - Only visible when expanded */}
              <AnimatePresence>
                {state.isExpanded && (
                  <motion.div
                    initial={{ opacity: 0, width: 0 }}
                    animate={{ opacity: 1, width: 'auto' }}
                    exit={{ opacity: 0, width: 0 }}
                    transition={{ duration: 0.2, ease: 'easeInOut' }}
                    className="ml-3 flex flex-1 items-center justify-between overflow-hidden"
                  >
                    <span className="text-sm font-medium whitespace-nowrap">
                      New Chat
                    </span>

                    {/* Action button - Original Style */}
                    <div className="w-6 h-6 rounded-md bg-muted group-hover:bg-[hsl(var(--primary))] transition-colors flex items-center justify-center flex-shrink-0">
                      <Plus className="w-3 h-3 text-muted-foreground group-hover:text-white" />
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </TooltipTrigger>
          <TooltipContent side="right">
            <p>New Chat</p>
          </TooltipContent>
        </Tooltip>

        {/* Other navigation items */}
        {navItems.slice(1).map((item) => {
          const IconComponent = item.icon;

          return (
            <Tooltip key={item.id}>
              <TooltipTrigger asChild>
                <div
                  className={cn(
                    'flex items-center transition-all duration-200 rounded-lg cursor-pointer group relative',
                    'hover:bg-[hsl(var(--primary))]/10 p-3',
                    item.isActive 
                      ? 'bg-[hsl(var(--primary))]/10 text-[hsl(var(--primary))]' 
                      : 'text-muted-foreground hover:text-[hsl(var(--primary))]'
                  )}
                  onClick={item.onClick}
                >
                  {/* Icon - Always visible and fixed position */}
                  <div className="flex h-4 w-4 flex-shrink-0 items-center justify-center">
                    <IconComponent className="h-4 w-4" />
                  </div>

                  {/* Text - Only visible when expanded */}
                  <AnimatePresence>
                    {state.isExpanded && (
                      <motion.div
                        initial={{ opacity: 0, width: 0 }}
                        animate={{ opacity: 1, width: 'auto' }}
                        exit={{ opacity: 0, width: 0 }}
                        transition={{ duration: 0.2, ease: 'easeInOut' }}
                        className="ml-3 overflow-hidden"
                      >
                        <span className="text-sm font-medium whitespace-nowrap">
                          {item.label}
                        </span>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              </TooltipTrigger>
              <TooltipContent side="right">
                <p>{item.label}</p>
              </TooltipContent>
            </Tooltip>
          );
        })}
      </div>

      {/* Recents Section - Only visible when expanded */}
      <AnimatePresence>
        {state.isExpanded && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.2, ease: 'easeInOut' }}
            className="flex-1 overflow-hidden"
          >
            <div className="px-3 py-4">
              <h3 className="mb-3 text-sm font-medium text-muted-foreground">Recents</h3>
            </div>

            <ScrollArea className="flex-1 px-3">
              <div className="space-y-1 pb-4">
                <AnimatePresence>
                  {recentChats.map((chat) => (
                    <motion.div
                      key={chat.id}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      className="group flex cursor-pointer items-center justify-between rounded-lg p-2 hover:bg-[hsl(var(--primary))]/10"
                      onClick={() => handleItemClick('chat-history')}
                    >
                      <div className="flex-1 min-w-0">
                        <p className="text-sm truncate text-foreground">{chat.title}</p>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <IconButton
                            variant="ghost"
                            className="h-6 w-6 opacity-0 group-hover:opacity-100"
                            aria-label="More options"
                            icon={<MoreHorizontal className="h-3 w-3" />}
                          />
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-40">
                          <DropdownMenuItem
                            onClick={() => handleChatAction('rename', chat.id)}
                          >
                            <Edit3 className="mr-2 h-3 w-3" />
                            Rename
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleChatAction('delete', chat.id)}
                            className="text-destructive"
                          >
                            <Trash2 className="mr-2 h-3 w-3" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </motion.div>
                  ))}
                </AnimatePresence>
              </div>
            </ScrollArea>
          </motion.div>
        )}
      </AnimatePresence>

      {/* User Profile Footer - At the very bottom like Claude */}
      <div className="mt-auto p-3">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <div
              className={cn(
                'group flex cursor-pointer items-center rounded-lg p-2 transition-colors hover:bg-[hsl(var(--primary))]/10'
              )}
            >
              <Avatar className="h-8 w-8 flex-shrink-0">
                <AvatarImage src={user.avatar} alt={user.name} />
                <AvatarFallback className="bg-[hsl(var(--primary))]/10 text-xs font-medium text-[hsl(var(--primary))]">
                  {user.initials}
                </AvatarFallback>
              </Avatar>

              <AnimatePresence>
                {state.isExpanded && (
                  <motion.div
                    initial={{ opacity: 0, width: 0 }}
                    animate={{ opacity: 1, width: 'auto' }}
                    exit={{ opacity: 0, width: 0 }}
                    transition={{ duration: 0.2, ease: 'easeInOut' }}
                    className="ml-3 min-w-0 flex-1 overflow-hidden text-left"
                  >
                    <p className="text-sm font-medium truncate text-foreground">{user.name}</p>
                    <p className="text-xs truncate text-muted-foreground">{user.email}</p>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className="w-56">
            <DropdownMenuLabel>My Account</DropdownMenuLabel>
            <DropdownMenuSeparator />

            <DropdownMenuItem>
              <User className="mr-2 h-4 w-4" />
              Profile
            </DropdownMenuItem>

            <DropdownMenuItem>
              <Settings className="mr-2 h-4 w-4" />
              Settings
            </DropdownMenuItem>

            <DropdownMenuSeparator />

            <DropdownMenuItem
              onClick={handleLogout}
              className="text-destructive focus:text-destructive"
            >
              <LogOut className="mr-2 h-4 w-4" />
              Sign out
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );

  // Mobile sidebar content component (no close button, that's handled by Sheet)
  const MobileSidebarContent = () => (
    <div className="flex flex-col h-full bg-background">
      {/* Header - KAVIA text and theme toggle */}
      <div className="flex items-center justify-between h-16 p-4 border-b border-border">
        <h1 className="text-lg font-semibold text-foreground">KAVIA</h1>
        <SimpleThemeToggle />
      </div>

      {/* Navigation Menu - Mobile optimized */}
      <div className="space-y-2 px-4 py-4">
        {/* New Chat - Mobile style */}
        <div
          className={cn(
            'flex items-center transition-all duration-200 rounded-lg cursor-pointer group relative',
            'hover:bg-[hsl(var(--primary))]/10 p-4',
            navItems[0].isActive 
              ? 'bg-[hsl(var(--primary))]/10 text-[hsl(var(--primary))]' 
              : 'text-muted-foreground hover:text-[hsl(var(--primary))]'
          )}
          onClick={() => handleItemClick('new-chat')}
        >
          {/* Icon */}
          <div className="mr-3 flex h-5 w-5 flex-shrink-0 items-center justify-center">
            <Plus className="h-5 w-5" />
          </div>

          {/* Text and Action */}
          <div className="flex flex-1 items-center justify-between">
            <span className="text-base font-medium">New Chat</span>

            {/* Action button */}
            <div className="w-7 h-7 rounded-md bg-muted group-hover:bg-[hsl(var(--primary))] transition-colors flex items-center justify-center flex-shrink-0">
              <Plus className="w-4 h-4 text-muted-foreground group-hover:text-white" />
            </div>
          </div>
        </div>

        {/* Other navigation items */}
        {navItems.slice(1).map((item) => {
          const IconComponent = item.icon;

          return (
            <div
              key={item.id}
              className={cn(
                'flex items-center transition-all duration-200 rounded-lg cursor-pointer group relative',
                'hover:bg-[hsl(var(--primary))]/10 p-4',
                item.isActive 
                  ? 'bg-[hsl(var(--primary))]/10 text-[hsl(var(--primary))]' 
                  : 'text-muted-foreground hover:text-[hsl(var(--primary))]'
              )}
              onClick={item.onClick}
            >
              {/* Icon */}
              <div className="mr-3 flex h-5 w-5 flex-shrink-0 items-center justify-center">
                <IconComponent className="h-5 w-5" />
              </div>

              {/* Text */}
              <span className="text-base font-medium">{item.label}</span>
            </div>
          );
        })}
      </div>

      {/* Recents Section - Mobile optimized */}
      <div className="flex-1 overflow-hidden">
        <div className="px-4 py-4">
          <h3 className="mb-4 text-sm font-medium tracking-wide uppercase text-muted-foreground">Recents</h3>
        </div>

        <ScrollArea className="flex-1 px-4">
          <div className="space-y-2 pb-4">
            {recentChats.map((chat) => (
              <div
                key={chat.id}
                className="group flex cursor-pointer items-center justify-between rounded-lg p-3 transition-colors hover:bg-[hsl(var(--primary))]/10"
                onClick={() => handleItemClick('chat-history')}
              >
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium truncate text-foreground">{chat.title}</p>
                  <p className="mt-1 text-xs text-muted-foreground">{chat.time}</p>
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <IconButton
                      variant="ghost"
                      className="h-8 w-8 opacity-0 group-hover:opacity-100"
                      aria-label="More options"
                      icon={<MoreHorizontal className="h-4 w-4" />}
                    />
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-40">
                    <DropdownMenuItem
                      onClick={() => handleChatAction('rename', chat.id)}
                    >
                      <Edit3 className="mr-2 h-4 w-4" />
                      Rename
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => handleChatAction('delete', chat.id)}
                      className="text-red-600"
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            ))}
          </div>
        </ScrollArea>
      </div>

      {/* User Profile Footer - Mobile optimized */}
      <div className="p-4 mt-auto border-t border-border">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <div
              className={cn(
                'group flex cursor-pointer items-center rounded-lg p-3 transition-colors hover:bg-[hsl(var(--primary))]/10'
              )}
            >
              <Avatar className="h-10 w-10 flex-shrink-0">
                <AvatarImage src={user.avatar} alt={user.name} />
                <AvatarFallback className="bg-[hsl(var(--primary))]/10 text-sm font-medium text-[hsl(var(--primary))]">
                  {user.initials}
                </AvatarFallback>
              </Avatar>
              
                  <div className="flex-1 min-w-0 ml-3 overflow-hidden text-left">
                    <p className="text-sm font-medium truncate text-foreground">{user.name}</p>
                    <p className="text-xs truncate text-muted-foreground">{user.email}</p>
                  </div>
            </div>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className="w-56">
            <DropdownMenuLabel>My Account</DropdownMenuLabel>
            <DropdownMenuSeparator />

            <DropdownMenuItem>
              <User className="mr-2 h-4 w-4" />
              Profile
            </DropdownMenuItem>

            <DropdownMenuItem>
              <Settings className="mr-2 h-4 w-4" />
              Settings
            </DropdownMenuItem>

            <DropdownMenuSeparator />

            <DropdownMenuItem
              onClick={handleLogout}
              className="text-red-600 focus:text-red-600"
            >
              <LogOut className="mr-2 h-4 w-4" />
              Sign out
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );

  // Mobile version using Sheet
  if (state.isMobile) {
    return (
      <Sheet open={state.isMobileMenuOpen} onOpenChange={closeMobileMenu}>
        <SheetContent
          side="left"
          className="w-80 p-0 bg-background border-r border-border focus:outline-none"
          onEscapeKeyDown={closeMobileMenu}
          onPointerDownOutside={closeMobileMenu}
          onInteractOutside={closeMobileMenu}
        >
          <SheetTitle className="sr-only">Navigation Menu</SheetTitle>
          <MobileSidebarContent />
        </SheetContent>
      </Sheet>
    );
  }

  // Desktop version
  return (
    <TooltipProvider>
      <motion.div
        initial={{
          width: state.isExpanded ? state.width : state.collapsedWidth,
        }}
        animate={{
          width: state.isExpanded ? state.width : state.collapsedWidth,
        }}
        transition={{
          type: 'spring',
          stiffness: 300,
          damping: 30,
          duration: 0.3,
        }}
        className={cn(
          'fixed left-0 top-0 z-50 h-full bg-background border-r border-border',
          'flex flex-col overflow-hidden shadow-sm',
          'hidden lg:flex', // Hide on mobile, show on desktop
          className
        )}
      >
        <DesktopSidebarContent />
      </motion.div>
    </TooltipProvider>
  );
}

export default UnifiedSidebar;
