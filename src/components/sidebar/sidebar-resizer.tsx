'use client';

import React, { useCallback, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useSidebar } from './sidebar-context';
import { useComponentStore } from '@/store/components';
import { cn } from '@/lib/utils';

interface SidebarResizerProps {
  className?: string;
  minWidth?: number;
  maxWidth?: number;
}

export function SidebarResizer({
  className,
  minWidth = 200,
  maxWidth = 400,
}: SidebarResizerProps) {
  const { state, setWidth } = useSidebar();
  const {
    sidebarIsResizing: isResizing,
    sidebarStartX: startX,
    sidebarStartWidth: startWidth,
    setSidebarIsResizing: setIsResizing,
    setSidebarStartX: setStartX,
    setSidebarStartWidth: setStartWidth,
  } = useComponentStore();
  const resizerRef = useRef<HTMLDivElement>(null);

  const handleMouseDown = useCallback(
    (e: React.MouseEvent) => {
      e.preventDefault();
      setIsResizing(true);
      setStartX(e.clientX);
      setStartWidth(state.width);
      document.body.style.cursor = 'col-resize';
      document.body.style.userSelect = 'none';
    },
    [state.width, setIsResizing, setStartX, setStartWidth]
  );

  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!isResizing) return;

      const deltaX = e.clientX - startX;
      const newWidth = Math.max(
        minWidth,
        Math.min(maxWidth, startWidth + deltaX)
      );

      setWidth(newWidth);
    },
    [isResizing, startX, startWidth, minWidth, maxWidth, setWidth]
  );

  const handleMouseUp = useCallback(() => {
    setIsResizing(false);
    document.body.style.cursor = '';
    document.body.style.userSelect = '';
  }, [setIsResizing]);

  const handleDoubleClick = useCallback(() => {
    // Reset to default width
    setWidth(280);
  }, [setWidth]);

  // Add global event listeners
  useEffect(() => {
    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);

      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
    return undefined;
  }, [isResizing, handleMouseMove, handleMouseUp]);

  // Prevent text selection during resize
  useEffect(() => {
    if (isResizing) {
      document.body.style.userSelect = 'none';
      return () => {
        document.body.style.userSelect = '';
      };
    }
    return undefined;
  }, [isResizing]);

  return (
    <motion.div
      ref={resizerRef}
      className={cn(
        'group absolute top-0 right-0 h-full w-1 cursor-col-resize',
        'hover:bg-primary/20 transition-colors duration-200',
        isResizing && 'bg-primary/30',
        className
      )}
      onMouseDown={handleMouseDown}
      onDoubleClick={handleDoubleClick}
      whileHover={{ scale: 1.2 }}
      transition={{ duration: 0.1 }}
    >
      {/* Visual indicator */}
      <div
        className={cn(
          'bg-border absolute inset-y-0 right-0 w-px',
          'group-hover:bg-primary/50 transition-colors duration-200',
          isResizing && 'bg-primary'
        )}
      />

      {/* Hover area */}
      <div className="absolute inset-y-0 -right-1 -left-1 w-3" />

      {/* Resize indicator dots */}
      <div
        className={cn(
          'absolute top-1/2 right-0 -translate-x-1/2 -translate-y-1/2 transform',
          'flex h-8 w-4 flex-col items-center justify-center space-y-0.5',
          'opacity-0 transition-opacity duration-200 group-hover:opacity-100',
          isResizing && 'opacity-100'
        )}
      >
        <div className="bg-muted-foreground h-0.5 w-0.5 rounded-full" />
        <div className="bg-muted-foreground h-0.5 w-0.5 rounded-full" />
        <div className="bg-muted-foreground h-0.5 w-0.5 rounded-full" />
      </div>
    </motion.div>
  );
}

export default SidebarResizer;
