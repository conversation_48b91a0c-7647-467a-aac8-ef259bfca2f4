'use client';

import React from 'react';
import { useSidebar } from './sidebar-context';
import { UnifiedSidebar } from './unified-sidebar';
import { cn } from '@/lib/utils';

interface SidebarProps {
  className?: string;
}

export function Sidebar({ className }: SidebarProps) {
  const { state } = useSidebar();

  // On mobile, always show sidebar (it's handled as a sheet in UnifiedSidebar)
  // On desktop, only show if open
  if (!state.isMobile && !state.isOpen) {
    return null;
  }

  return (
    <div className={cn('relative', className)}>
      <UnifiedSidebar />

      {/* Spacer for layout - only on desktop */}
      {!state.isMobile && (
        <div
          style={{
            width: state.isExpanded ? state.width : state.collapsedWidth,
          }}
          className="flex-shrink-0"
        />
      )}
    </div>
  );
}

export default Sidebar;
