'use client';

import React from 'react';
import { Menu } from 'lucide-react';
import { IconButton } from '@/components/ui/icon-button';
import { useSidebar } from './sidebar-context';
import { cn } from '@/lib/utils';

interface MobileHamburgerProps {
  className?: string;
}

export function MobileHamburger({ className }: MobileHamburgerProps) {
  const { state, toggleMobileMenu } = useSidebar();

  // Only show on mobile when sidebar is NOT open
  if (!state.isMobile || state.isMobileMenuOpen) {
    return null;
  }

  return (
    <IconButton
      variant="ghost"
      onClick={toggleMobileMenu}
      className={cn(
        'fixed top-4 left-4 z-[60] lg:hidden',
        'h-10 w-10 rounded-xl hover:bg-[hsl(var(--primary))]/10',
        'bg-background/95 shadow-sm backdrop-blur-sm transition-all duration-200',
        'border border-border/50',
        className
      )}
      aria-label="Open navigation menu"
      icon={<Menu className="h-5 w-5 text-[hsl(var(--primary))]" />}
    />
  );
}
