'use client';

import { useEffect } from 'react';
import { useSidebar } from './sidebar-context';

export function SidebarShortcuts() {
  const { toggle, expand, collapse, setActiveItem } = useSidebar();

  useEffect(() => {
    const handleKeydown = (event: KeyboardEvent) => {
      // Check for modifier keys (Cmd on Mac, Ctrl on Windows/Linux)
      const isModifierPressed = event.metaKey || event.ctrlKey;

      if (!isModifierPressed) return;

      switch (event.key) {
        case 'b':
        case 'B':
          // Cmd/Ctrl + B to toggle expand/collapse
          event.preventDefault();
          toggle();
          break;

        case '\\':
          // Cmd/Ctrl + \ to toggle expand/collapse (VS Code style)
          event.preventDefault();
          toggle();
          break;

        case '1':
          // Cmd/Ctrl + 1 to go to home
          event.preventDefault();
          setActiveItem('home');
          break;

        case '2':
          // Cmd/Ctrl + 2 to go to projects
          event.preventDefault();
          setActiveItem('projects');
          break;

        case 'n':
        case 'N':
          // Cmd/Ctrl + N for new chat
          if (event.shiftKey) {
            event.preventDefault();
            setActiveItem('new-chat');
          }
          break;
      }
    };

    document.addEventListener('keydown', handleKeydown);
    return () => document.removeEventListener('keydown', handleKeydown);
  }, [toggle, expand, collapse, setActiveItem]);

  return null; // This component doesn't render anything
}

export default SidebarShortcuts;
