'use client';

import React, { ReactNode, useEffect } from 'react';
import { Sidebar } from './sidebar';
import { SidebarShortcuts } from './sidebar-shortcuts';
import { MobileHamburger } from './mobile-hamburger';
import { useInitializeSidebar } from './use-sidebar-data';
import { useSidebar } from './sidebar-context';
import { MainContent } from '@/components/content/main-content';
import { cn } from '@/lib/utils';

interface SidebarLayoutProps {
  children?: ReactNode;
  className?: string;
  showMainContent?: boolean;
}

export function SidebarLayout({
  children,
  className,
  showMainContent = false,
}: SidebarLayoutProps) {
  const { state } = useSidebar();
  useInitializeSidebar();

  // Handle responsive behavior
  useEffect(() => {
    const handleResize = () => {
      // Auto-close sidebar on small screens when not pinned
      if (window.innerWidth < 1024 && !state.isPinned && state.isOpen) {
        // Don't auto-close, let user control
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [state.isPinned, state.isOpen]);

  return (
    <div className={cn('flex h-screen overflow-hidden', className)}>
      {/* Keyboard shortcuts handler */}
      <SidebarShortcuts />

      {/* Mobile hamburger menu */}
      <MobileHamburger />

      {/* Sidebar - only on desktop */}
      <Sidebar />

      {/* Main content area */}
      <div
        className={cn(
          'flex min-h-0 flex-1 flex-col overflow-hidden',
          // Ensure proper spacing from sidebar on desktop only
          state.isMobile ? 'w-full' : ''
        )}
      >
        {/* Mobile top spacer for hamburger menu */}
        {state.isMobile && <div className="h-16 flex-shrink-0" />}

        {/* Main content */}
        <main
          className={cn(
            'min-h-0 flex-1 overflow-auto',
            // Mobile horizontal padding only
            state.isMobile ? 'px-2' : ''
          )}
        >
          {showMainContent ? <MainContent /> : children}
        </main>
      </div>
    </div>
  );
}

export default SidebarLayout;
