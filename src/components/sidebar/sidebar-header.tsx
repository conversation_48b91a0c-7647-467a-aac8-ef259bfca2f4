'use client';

import React from 'react';
import Link from 'next/link';
import { Message<PERSON><PERSON><PERSON>, Pin, <PERSON>nO<PERSON>, <PERSON>u, Plus } from 'lucide-react';
import { IconButton } from '@/components/ui/icon-button';
import { Button } from '@/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { useSidebar } from './sidebar-context';
import { useTenant } from '@/components/tenant/tenant-provider';
import { cn } from '@/lib/utils';

interface SidebarHeaderProps {
  className?: string;
  showLogo?: boolean;
  showControls?: boolean;
}

export function SidebarHeader({
  className,
  showLogo = true,
  showControls = true,
}: SidebarHeaderProps) {
  const { state, toggle, togglePin } = useSidebar();
  const { tenantSlug, tenant } = useTenant();

  return (
    <TooltipProvider>
      <div
        className={cn(
          'border-border flex items-center justify-between border-b p-4',
          'bg-background/95 min-h-[60px] backdrop-blur-sm',
          className
        )}
      >
        {/* Logo and Brand */}
        {showLogo && (
          <Link
            href={`/${tenantSlug}/projects`}
            className="flex items-center space-x-2 transition-opacity hover:opacity-80"
          >
            <div className="bg-primary flex h-8 w-8 items-center justify-center rounded-lg">
              <MessageSquare className="text-primary-foreground h-5 w-5" />
            </div>
            <div className="flex flex-col">
              <span className="text-foreground text-sm font-semibold">
                {tenant?.name || 'KAVIA'}
              </span>
              <span className="text-muted-foreground text-xs">
                AI Assistant
              </span>
            </div>
          </Link>
        )}

        {/* Controls */}
        {showControls && (
          <div className="flex items-center space-x-1">
            {/* New Chat Button */}
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" asChild className="h-8 w-8 p-0">
                  <Link href={`/${tenantSlug}/chat`}>
                    <Plus className="h-4 w-4" />
                  </Link>
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>New Chat</p>
              </TooltipContent>
            </Tooltip>

            {/* Pin Toggle */}
            <Tooltip>
              <TooltipTrigger asChild>
                <IconButton
                  variant="ghost"
                  onClick={togglePin}
                  aria-label={state.isPinned ? 'Unpin Sidebar' : 'Pin Sidebar'}
                  icon={
                    state.isPinned ? (
                      <PinOff className="h-4 w-4" />
                    ) : (
                      <Pin className="h-4 w-4" />
                    )
                  }
                />
              </TooltipTrigger>
              <TooltipContent>
                <p>{state.isPinned ? 'Unpin Sidebar' : 'Pin Sidebar'}</p>
              </TooltipContent>
            </Tooltip>

            {/* Collapse Toggle */}
            <Tooltip>
              <TooltipTrigger asChild>
                <IconButton
                  variant="ghost"
                  onClick={toggle}
                  aria-label="Toggle Sidebar"
                  icon={<Menu className="h-4 w-4" />}
                />
              </TooltipTrigger>
              <TooltipContent>
                <p>Toggle Sidebar</p>
              </TooltipContent>
            </Tooltip>
          </div>
        )}
      </div>
    </TooltipProvider>
  );
}

export default SidebarHeader;
