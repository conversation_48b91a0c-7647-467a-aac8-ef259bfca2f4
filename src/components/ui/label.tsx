import * as React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';

const labelVariants = cva(
  'text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70',
  {
    variants: {
      size: {
        sm: 'text-sm',
        md: 'text-sm',
        lg: 'text-base',
      },
      muted: {
        true: 'text-muted-foreground',
        false: '',
      },
    },
    defaultVariants: {
      size: 'md',
      muted: false,
    },
  }
);

type LabelProps = React.ComponentProps<'label'> &
  VariantProps<typeof labelVariants> & {
    required?: boolean;
  };

const Label = React.forwardRef<HTMLLabelElement, LabelProps>(
  ({ className, required = false, size, muted, children, ...props }, ref) => {
    return (
      <label
        ref={ref}
        data-slot="label"
        className={cn(
          labelVariants({ size, muted }),
          required && "after:text-destructive after:ml-0.5 after:content-['*']",
          className
        )}
        aria-required={required || undefined}
        {...props}
      >
        {children}
      </label>
    );
  }
);
Label.displayName = 'Label';

export { Label, labelVariants };
