import * as React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';

// Runtime-injected shimmer stylesheet (client-only, no Tailwind config needed)
let __kaviaSkeletonShimmerInjected = false;
function ensureShimmerCSS() {
  if (typeof document === 'undefined' || __kaviaSkeletonShimmerInjected) return;
  const style = document.createElement('style');
  style.setAttribute('data-kavia-skeleton', 'shimmer');
  style.textContent = `
@keyframes kavia-skeleton-shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}
.k-skeleton-shimmer::after {
  content: "";
  position: absolute;
  inset: 0;
  background: linear-gradient(90deg,
    hsl(var(--background) / 0) 0%,
    hsl(var(--background) / 0.35) 50%,
    hsl(var(--background) / 0) 100%
  );
  transform: translateX(-100%);
  animation: kavia-skeleton-shimmer 1.5s infinite;
  pointer-events: none;
}
.dark .k-skeleton-shimmer::after {
  background: linear-gradient(90deg,
    hsl(var(--foreground) / 0) 0%,
    hsl(var(--foreground) / 0.25) 50%,
    hsl(var(--foreground) / 0) 100%
  );
}
`;
  document.head.appendChild(style);
  __kaviaSkeletonShimmerInjected = true;
}

const skeletonVariants = cva('relative overflow-hidden bg-muted', {
  variants: {
    shape: {
      rect: '',
      text: 'h-4',
      circle: '',
    },
    radius: {
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full',
    },
    animated: {
      true: 'animate-pulse',
      false: '',
    },
  },
  defaultVariants: {
    shape: 'rect',
    radius: 'md',
    animated: true,
  },
});

type SkeletonBaseProps = React.HTMLAttributes<HTMLDivElement>;
type SkeletonVariants = VariantProps<typeof skeletonVariants>;
type SkeletonProps = SkeletonBaseProps &
  SkeletonVariants & {
    withShimmer?: boolean;
  };

/**
 * Visual placeholder used while content is loading.
 * - Provide explicit width/height via className for layout stability.
 * - shape="text" supplies a default height; others rely on caller dimensions.
 * - withShimmer adds a subtle moving highlight (client-only).
 */
function Skeleton({
  className,
  shape,
  radius,
  animated,
  withShimmer = false,
  ...props
}: SkeletonProps) {
  React.useEffect(() => {
    if (withShimmer) ensureShimmerCSS();
  }, [withShimmer]);

  const computedRadius = shape === 'circle' ? 'full' : radius;

  return (
    <div
      aria-hidden="true"
      data-slot="skeleton"
      className={cn(
        skeletonVariants({ shape, radius: computedRadius, animated }),
        withShimmer && 'k-skeleton-shimmer',
        className
      )}
      {...props}
    />
  );
}

/**
 * Renders multiple text-like skeleton lines with consistent spacing.
 * The last line is shorter to mimic paragraph raggedness.
 */
type SkeletonTextProps = {
  lines?: number;
  lineClassName?: string;
  animated?: SkeletonVariants['animated'];
  withShimmer?: boolean;
  className?: string;
};
function SkeletonText({
  lines = 3,
  lineClassName,
  animated = true,
  withShimmer = false,
  className,
}: SkeletonTextProps) {
  const arr = Array.from({ length: Math.max(1, lines) });
  return (
    <div data-slot="skeleton-text" className={cn('space-y-2', className)}>
      {arr.map((_, i) => (
        <Skeleton
          key={i}
          shape="text"
          animated={animated}
          withShimmer={withShimmer}
          className={cn(
            i === arr.length - 1 ? 'w-2/3' : 'w-full',
            lineClassName
          )}
        />
      ))}
    </div>
  );
}

/**
 * Circular avatar skeleton with preset sizes.
 */
type AvatarSkeletonProps = Omit<SkeletonProps, 'shape' | 'radius'> & {
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
};
const avatarSizeClass: Record<
  NonNullable<AvatarSkeletonProps['size']>,
  string
> = {
  xs: 'size-6',
  sm: 'size-8',
  md: 'size-10',
  lg: 'size-12',
  xl: 'size-16',
};
function AvatarSkeleton({
  size = 'md',
  className,
  animated = true,
  withShimmer,
  ...props
}: AvatarSkeletonProps) {
  return (
    <Skeleton
      shape="circle"
      animated={animated}
      withShimmer={withShimmer}
      className={cn(avatarSizeClass[size], className)}
      {...props}
    />
  );
}

export { Skeleton, SkeletonText, AvatarSkeleton, skeletonVariants };
