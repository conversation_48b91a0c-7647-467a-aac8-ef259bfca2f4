import * as React from 'react';
import { cn } from '@/lib/utils';

/**
 * Segmented action group for composing multiple Buttons together.
 * - Applies contiguous rounding and merged borders
 * - Works with any child that has data-slot="button" (our Button does)
 * - Horizontal (default) or vertical orientation
 * - Optional equalWidth to make all buttons fill available space
 *
 * Accessibility:
 * - Use aria-label or aria-labelledby to describe the group purpose.
 */
type ButtonGroupProps = React.HTMLAttributes<HTMLDivElement> & {
  orientation?: 'horizontal' | 'vertical';
  attached?: boolean;
  equalWidth?: boolean;
};

function ButtonGroup({
  className,
  orientation = 'horizontal',
  attached = true,
  equalWidth = false,
  ...props
}: ButtonGroupProps) {
  if (process.env.NODE_ENV !== 'production') {
    if (!('aria-label' in props) && !('aria-labelledby' in props)) {
      console.warn(
        'ButtonGroup should have an aria-label or aria-labelledby for accessibility.'
      );
    }
  }

  const baseLayout =
    orientation === 'vertical' ? 'inline-flex flex-col' : 'inline-flex';

  // Child selectors target our Button via data-slot set in Button component
  const attachedClasses =
    orientation === 'vertical'
      ? [
          "[&>[data-slot='button']]:rounded-none",
          "[&>[data-slot='button']]:first:rounded-t-md",
          "[&>[data-slot='button']]:last:rounded-b-md",
          "[&>[data-slot='button']:not(:first-child)]:-mt-px",
          "[&>[data-slot='button']]:focus-visible:z-10",
        ].join(' ')
      : [
          "[&>[data-slot='button']]:rounded-none",
          "[&>[data-slot='button']]:first:rounded-l-md",
          "[&>[data-slot='button']]:last:rounded-r-md",
          "[&>[data-slot='button']:not(:first-child)]:-ml-px",
          "[&>[data-slot='button']]:focus-visible:z-10",
        ].join(' ');

  const widthClasses = equalWidth ? "[&>[data-slot='button']]:flex-1" : '';

  return (
    <div
      role="group"
      data-slot="button-group"
      className={cn(
        baseLayout,
        attached && attachedClasses,
        widthClasses,
        className
      )}
      {...props}
    />
  );
}

export { ButtonGroup };
