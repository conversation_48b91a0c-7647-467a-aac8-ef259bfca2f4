'use client';

import * as React from 'react';
import * as TogglePrimitive from '@radix-ui/react-toggle';
import { cn } from '@/lib/utils';

type ToggleProps = React.ComponentProps<typeof TogglePrimitive.Root>;

const Toggle = React.forwardRef<
  React.ElementRef<typeof TogglePrimitive.Root>,
  ToggleProps
>(({ className, ...props }, ref) => {
  return (
    <TogglePrimitive.Root
      ref={ref}
      data-slot="toggle"
      className={cn(
        'inline-flex items-center justify-center rounded-md border border-transparent px-3 py-1 text-sm font-medium shadow-xs transition-[color,box-shadow,background-color,border-color] outline-none',
        'bg-background hover:bg-accent hover:text-accent-foreground',
        'focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]',
        'data-[state=on]:bg-primary data-[state=on]:text-primary-foreground',
        'disabled:cursor-not-allowed disabled:opacity-50',
        className
      )}
      {...props}
    />
  );
});
Toggle.displayName = 'Toggle';

export { Toggle };
