'use client';

import * as React from 'react';
import { Toaster as SonnerToaster, toast as sonnerToast } from 'sonner';

/**
 * Kavia-themed Sonner Toaster wrapper.
 * Usage:
 * - Render <Toaster /> once near root (e.g. in layout).
 * - Import { toast } to show notifications.
 */
type ToasterProps = React.ComponentProps<typeof SonnerToaster>;

function Toaster({
  position = 'top-right',
  closeButton = true,
  richColors = true,
  theme = 'system',
  toastOptions,
  ...props
}: ToasterProps) {
  return (
    <SonnerToaster
      position={position}
      closeButton={closeButton}
      richColors={richColors}
      theme={theme}
      toastOptions={{
        duration: 3500,
        classNames: {
          toast:
            // container
            'bg-background text-foreground border border-border shadow-sm',
          title: 'font-medium',
          description: 'text-muted-foreground',
          actionButton:
            'bg-primary text-primary-foreground hover:bg-primary/90',
          cancelButton: 'bg-muted text-foreground hover:bg-muted/80',
          closeButton: 'text-muted-foreground hover:text-foreground',
        },
        ...toastOptions,
      }}
      {...props}
    />
  );
}

/**
 * Re-export toast for convenience:
 * toast('Message')
 * toast.success('Saved')
 * toast.error('Failed')
 * toast.info('Heads up')
 * toast.warning('Careful')
 */
const toast = sonnerToast;

export { Toaster, toast };
