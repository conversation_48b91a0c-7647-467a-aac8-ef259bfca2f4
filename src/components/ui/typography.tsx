import * as React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';

/**
 * Heading
 * - Semantic defaults map size -> h1..h4; can be overridden via "as"
 * - Use align and muted to keep presentation consistent across pages
 */
const headingVariants = cva('text-foreground scroll-m-20', {
  variants: {
    size: {
      h1: 'text-4xl md:text-5xl font-bold tracking-tight',
      h2: 'text-3xl md:text-4xl font-semibold tracking-tight',
      h3: 'text-2xl font-semibold tracking-tight',
      h4: 'text-xl font-semibold tracking-tight',
    },
    align: {
      left: 'text-left',
      center: 'text-center',
      right: 'text-right',
    },
    muted: {
      true: 'text-muted-foreground',
      false: '',
    },
  },
  defaultVariants: {
    size: 'h2',
    align: 'left',
    muted: false,
  },
});

type HeadingVariants = VariantProps<typeof headingVariants>;
type HeadingTag = 'h1' | 'h2' | 'h3' | 'h4';
const defaultHeadingTagBySize: Record<
  NonNullable<HeadingVariants['size']>,
  HeadingTag
> = {
  h1: 'h1',
  h2: 'h2',
  h3: 'h3',
  h4: 'h4',
};

type HeadingProps = Omit<React.HTMLAttributes<HTMLHeadingElement>, 'color'> &
  HeadingVariants & {
    as?: HeadingTag;
  };

const Heading = React.forwardRef<HTMLHeadingElement, HeadingProps>(
  function Heading(
    { className, size, align, muted, as, children, ...props },
    ref
  ) {
    const Tag = as ?? defaultHeadingTagBySize[size ?? 'h2'];
    return (
      <Tag
        ref={ref}
        data-slot="heading"
        className={cn(headingVariants({ size, align, muted }), className)}
        {...props}
      >
        {children}
      </Tag>
    );
  }
);

/**
 * Subheading (eyebrow/section label)
 * - Uppercased by default, muted tone
 */
const subheadingVariants = cva(
  'text-muted-foreground uppercase tracking-wide font-medium',
  {
    variants: {
      size: {
        sm: 'text-xs',
        md: 'text-xs md:text-sm',
      },
      align: {
        left: 'text-left',
        center: 'text-center',
        right: 'text-right',
      },
      underline: {
        true: 'underline underline-offset-4',
        false: '',
      },
    },
    defaultVariants: {
      size: 'md',
      align: 'left',
      underline: false,
    },
  }
);

type SubheadingProps = React.HTMLAttributes<HTMLParagraphElement> &
  VariantProps<typeof subheadingVariants> & {
    as?: 'p' | 'div' | 'span';
  };

const Subheading = React.forwardRef<HTMLParagraphElement, SubheadingProps>(
  function Subheading(
    { className, size, align, underline, as, children, ...props },
    ref
  ) {
    const Tag = (as ?? 'p') as unknown as React.ElementType;
    return (
      <Tag
        ref={ref}
        data-slot="subheading"
        className={cn(
          subheadingVariants({ size, align, underline }),
          className
        )}
        {...props}
      >
        {children}
      </Tag>
    );
  }
);

/**
 * Text
 * - For body copy; compose with "Muted" or muted variant
 */
const textVariants = cva('text-foreground', {
  variants: {
    size: {
      xs: 'text-xs',
      sm: 'text-sm',
      md: 'text-base',
      lg: 'text-lg',
    },
    align: {
      left: 'text-left',
      center: 'text-center',
      right: 'text-right',
    },
    weight: {
      normal: 'font-normal',
      medium: 'font-medium',
      semibold: 'font-semibold',
    },
    leading: {
      tight: 'leading-tight',
      snug: 'leading-snug',
      normal: 'leading-normal',
      relaxed: 'leading-relaxed',
    },
    muted: {
      true: 'text-muted-foreground',
      false: '',
    },
  },
  defaultVariants: {
    size: 'md',
    align: 'left',
    weight: 'normal',
    leading: 'normal',
    muted: false,
  },
});

type TextProps = React.HTMLAttributes<HTMLParagraphElement> &
  VariantProps<typeof textVariants> & {
    as?: 'p' | 'span' | 'div';
  };

const Text = React.forwardRef<HTMLParagraphElement, TextProps>(function Text(
  { className, size, align, weight, leading, muted, as, children, ...props },
  ref
) {
  const Tag = (as ?? 'p') as unknown as React.ElementType;
  return (
    <Tag
      ref={ref}
      data-slot="text"
      className={cn(
        textVariants({ size, align, weight, leading, muted }),
        className
      )}
      {...props}
    >
      {children}
    </Tag>
  );
});

/**
 * Muted
 * - Convenience wrapper for subdued inline or block text
 */
type MutedProps = Omit<TextProps, 'muted'>;

const Muted = React.forwardRef<HTMLParagraphElement, MutedProps>(function Muted(
  { className, ...props },
  ref
) {
  return (
    <Text ref={ref} data-slot="muted" muted className={className} {...props} />
  );
});

export {
  Heading,
  Subheading,
  Text,
  Muted,
  headingVariants,
  textVariants,
  subheadingVariants,
};
