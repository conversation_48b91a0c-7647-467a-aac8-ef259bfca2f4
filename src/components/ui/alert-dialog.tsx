'use client';

import * as React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription,
} from './dialog';
import { Button } from './button';

type AlertDialogProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: React.ReactNode;
  description?: React.ReactNode;
  cancelText?: React.ReactNode;
  actionText?: React.ReactNode;
  actionVariant?: React.ComponentProps<typeof Button>['variant'];
  /**
   * Called when user confirms (clicks action button).
   * If it returns a promise, button will show loading state until it resolves.
   */
  onAction?: () => void | Promise<void>;
  /**
   * Optional children rendered before footer.
   */
  children?: React.ReactNode;
};

export function AlertDialog({
  open,
  onOpenChange,
  title,
  description,
  cancelText = 'Cancel',
  actionText = 'Continue',
  actionVariant = 'destructive',
  onAction,
  children,
}: AlertDialogProps) {
  const [loading, setLoading] = React.useState(false);

  async function handleAction() {
    if (!onAction) {
      onOpenChange(false);
      return;
    }
    try {
      const p = onAction();
      if (p && typeof (p as Promise<void>).then === 'function') {
        setLoading(true);
        await (p as Promise<void>);
      }
      onOpenChange(false);
    } finally {
      setLoading(false);
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        role="alertdialog"
        aria-describedby="alert-dialog-description"
      >
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          {description ? (
            <DialogDescription id="alert-dialog-description">
              {description}
            </DialogDescription>
          ) : null}
        </DialogHeader>

        {children}

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            {cancelText}
          </Button>
          <Button
            variant={actionVariant}
            onClick={handleAction}
            isLoading={loading}
            loadingText={
              typeof actionText === 'string' ? actionText : undefined
            }
          >
            {actionText}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
