import * as React from 'react';
import { cn } from '@/lib/utils';
import { useFormField } from '@/components/ui/form-field';

type FieldDescriptionProps = React.ComponentProps<'p'>;

function FieldDescription({
  className,
  children,
  ...props
}: FieldDescriptionProps) {
  const { descriptionId, setHasDescription } = useFormField();

  React.useEffect(() => {
    setHasDescription(true);
    return () => setHasDescription(false);
  }, [setHasDescription]);

  return (
    <p
      id={descriptionId}
      data-slot="field-description"
      className={cn('text-muted-foreground mt-1 text-sm', className)}
      {...props}
    >
      {children}
    </p>
  );
}

type FieldErrorProps = React.ComponentProps<'p'>;

function FieldError({ className, children, ...props }: FieldErrorProps) {
  const { errorId, setHasError } = useFormField();

  React.useEffect(() => {
    setHasError(true);
    return () => setHasError(false);
  }, [setHasError]);

  return (
    <p
      id={errorId}
      role="alert"
      data-slot="field-error"
      className={cn('text-destructive mt-1 text-sm', className)}
      {...props}
    >
      {children}
    </p>
  );
}

/**
 * Convenience wrapper that renders either a description or an error message
 * based on the "variant" provided.
 */
type FieldMessageProps =
  | ({ variant: 'description' } & FieldDescriptionProps)
  | ({ variant: 'error' } & FieldErrorProps);

function FieldMessage(props: FieldMessageProps) {
  if (props.variant === 'error') {
    const { variant, ...rest } = props;
    void variant;
    return <FieldError {...(rest as FieldErrorProps)} />;
  }
  const { variant, ...rest } = props;
  void variant;
  return <FieldDescription {...(rest as FieldDescriptionProps)} />;
}

export { FieldDescription, FieldError, FieldMessage };
