import * as React from 'react';
import { cn } from '@/lib/utils';
import { Label } from './label';

type FormFieldContextValue = {
  fieldId: string;
  labelId: string;
  descriptionId: string;
  errorId: string;
  required: boolean;
  invalid: boolean;
  hasDescription: boolean;
  hasError: boolean;
  setHasDescription: (v: boolean) => void;
  setHasError: (v: boolean) => void;
  describedBy?: string;
};

const FormFieldContext = React.createContext<FormFieldContextValue | null>(
  null
);

function useFormField(): FormFieldContextValue {
  const ctx = React.useContext(FormFieldContext);
  if (!ctx) {
    throw new Error('useFormField must be used within a <FormField>');
  }
  return ctx;
}

type FormFieldProps = React.HTMLAttributes<HTMLDivElement> & {
  id?: string;
  required?: boolean;
  invalid?: boolean;
  /**
   * Additional ids to include in aria-describedby besides description/error.
   */
  describedBy?: string;
};

/**
 * Provides accessible wiring (ids, aria-*) for Label, Control, and Field messages.
 * Ensures consistent vertical rhythm via default spacing.
 */
function FormField({
  id,
  required = false,
  invalid = false,
  describedBy,
  className,
  children,
  ...props
}: FormFieldProps) {
  const reactId = React.useId();
  const baseId = React.useMemo(
    () => id ?? `field-${reactId.replace(/:/g, '')}`,
    [id, reactId]
  );

  const [hasDescription, setHasDescription] = React.useState(false);
  const [hasError, setHasError] = React.useState(false);

  const value = React.useMemo<FormFieldContextValue>(
    () => ({
      fieldId: baseId,
      labelId: `${baseId}-label`,
      descriptionId: `${baseId}-description`,
      errorId: `${baseId}-error`,
      required,
      invalid: invalid || hasError,
      hasDescription,
      hasError,
      setHasDescription,
      setHasError,
      describedBy,
    }),
    [baseId, required, invalid, hasError, hasDescription, describedBy]
  );

  return (
    <div
      data-slot="form-field"
      className={cn('space-y-1.5', className)}
      {...props}
    >
      <FormFieldContext.Provider value={value}>
        {children}
      </FormFieldContext.Provider>
    </div>
  );
}

type FormLabelProps = React.ComponentProps<typeof Label>;

/**
 * Composes Label with the current field context for htmlFor/required.
 */
function FormLabel({ className, htmlFor, required, ...props }: FormLabelProps) {
  const { labelId, fieldId, required: ctxRequired } = useFormField();
  return (
    <Label
      id={labelId}
      htmlFor={htmlFor ?? fieldId}
      required={required ?? ctxRequired}
      className={className}
      {...props}
    />
  );
}

type FormControlProps = {
  children: React.ReactElement<{
    id?: string;
    required?: boolean;
    'aria-required'?: boolean;
    'aria-invalid'?: boolean;
    'aria-describedby'?: string;
  }>;
  /**
   * Force invalid state on the control only (overrides context invalid)
   */
  invalid?: boolean;
  /**
   * Extra ids to include in aria-describedby for this control.
   */
  describedBy?: string;
} & React.HTMLAttributes<HTMLDivElement>;

/**
 * Injects accessibility attributes into a single form control element.
 * Clones the child and merges id, required, aria-invalid, aria-describedby.
 */
function FormControl({
  children,
  invalid,
  describedBy,
  ...wrapperProps
}: FormControlProps) {
  const {
    fieldId,
    descriptionId,
    errorId,
    required,
    invalid: ctxInvalid,
    hasDescription,
    hasError,
    describedBy: ctxDescribedBy,
  } = useFormField();

  const computedInvalid = invalid ?? ctxInvalid;

  const describedIds = [
    ctxDescribedBy,
    describedBy,
    children.props?.['aria-describedby'],
    hasDescription ? descriptionId : undefined,
    hasError ? errorId : undefined,
  ]
    .filter(Boolean)
    .join(' ')
    .trim();

  const cloned = React.cloneElement(children, {
    id: children.props?.id ?? fieldId,
    required: children.props?.required ?? required,
    'aria-required':
      children.props?.['aria-required'] ?? (required || undefined),
    'aria-invalid':
      children.props?.['aria-invalid'] ?? (computedInvalid || undefined),
    'aria-describedby': describedIds || undefined,
  });

  // Wrapper allows flex/grid layouts when needed by consumers
  return (
    <div data-slot="form-control" {...wrapperProps}>
      {cloned}
    </div>
  );
}

export { FormField, useFormField, FormLabel, FormControl };
