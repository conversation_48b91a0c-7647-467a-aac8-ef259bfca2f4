import Image from 'next/image';

interface KaviaLogoProps {
  className?: string;
  logoSize?: 'sm' | 'md' | 'lg';
  textSize?: 'sm' | 'md' | 'lg';
}

export function KaviaLogo({
  className = '',
  logoSize = 'md',
  textSize = 'md'
}: KaviaLogoProps) {
  const logoSizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-10 h-12',
    lg: 'w-12 h-14'
  };

  const textSizeClasses = {
    sm: 'text-lg',
    md: 'text-xl',
    lg: 'text-2xl'
  };

  return (
    <div className={`flex items-center gap-3 ${className}`}>
      <Image
        src="/kavia-logo.svg"
        alt="Kavia Logo"
        width={54}
        height={62}
        className={logoSizeClasses[logoSize]}
        priority
      />
      <span
        className={`font-sans font-semibold text-foreground tracking-wide ${textSizeClasses[textSize]}`}
      >
        KAVIA AI
      </span>
    </div>
  );
}