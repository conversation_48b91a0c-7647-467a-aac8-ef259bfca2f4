'use client';

import * as React from 'react';
import * as SliderPrimitive from '@radix-ui/react-slider';
import { cn } from '@/lib/utils';

type RootProps = React.ComponentProps<typeof SliderPrimitive.Root>;

/**
 * Accessible, theme-consistent slider with multi-thumb support.
 * Renders as many thumbs as the provided value/defaultValue length.
 */
function Slider({ className, children, ...props }: RootProps) {
  const thumbCount =
    (Array.isArray(props.value) && props.value.length) ||
    (Array.isArray(props.defaultValue) && props.defaultValue.length) ||
    1;

  return (
    <SliderPrimitive.Root
      data-slot="slider"
      className={cn(
        'relative flex w-full touch-none items-center select-none',
        className
      )}
      {...props}
    >
      <SliderPrimitive.Track
        data-slot="slider-track"
        className="bg-muted relative h-2 w-full grow overflow-hidden rounded-full"
      >
        <SliderPrimitive.Range
          data-slot="slider-range"
          className="bg-primary absolute h-full"
        />
      </SliderPrimitive.Track>

      {children
        ? children
        : Array.from({ length: thumbCount }).map((_, i) => (
            <SliderPrimitive.Thumb
              key={i}
              data-slot="slider-thumb"
              className={cn(
                'bg-background border-input ring-offset-background block size-5 rounded-full border shadow-sm outline-none',
                'focus-visible:ring-ring/50 focus-visible:ring-[3px]'
              )}
            />
          ))}
    </SliderPrimitive.Root>
  );
}

export { Slider };
