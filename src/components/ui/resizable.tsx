'use client';

import * as React from 'react';
import {
  PanelGroup,
  Panel,
  PanelResizeHandle,
  type PanelGroupProps,
  type PanelProps,
} from 'react-resizable-panels';
import { cn } from '@/lib/utils';

function ResizablePanelGroup({
  className,
  ...props
}: PanelGroupProps & { className?: string }) {
  return (
    <PanelGroup
      data-slot="resizable-panel-group"
      className={cn('flex h-full w-full', className)}
      {...props}
    />
  );
}

function ResizablePanel({
  className,
  ...props
}: PanelProps & { className?: string }) {
  return (
    <Panel
      data-slot="resizable-panel"
      className={cn('flex min-w-0', className)}
      {...props}
    />
  );
}

type ResizableHandleProps = React.ComponentProps<typeof PanelResizeHandle> & {
  orientation?: 'horizontal' | 'vertical';
  withHandle?: boolean;
};

function ResizableHandle({
  className,
  orientation = 'vertical',
  withHandle = true,
  ...props
}: ResizableHandleProps) {
  const isVertical = orientation === 'vertical'; // vertical = panels side-by-side
  return (
    <PanelResizeHandle
      data-slot="resizable-handle"
      className={cn(
        'group/handle bg-border relative flex shrink-0 items-center justify-center',
        isVertical
          ? 'h-full w-px cursor-col-resize'
          : 'h-px w-full cursor-row-resize',
        className
      )}
      {...props}
    >
      {withHandle ? (
        <div
          className={cn(
            'bg-muted-foreground/30 rounded-xs opacity-0 transition-opacity group-hover/handle:opacity-100',
            isVertical ? 'h-8 w-1' : 'h-1 w-8'
          )}
        />
      ) : null}
    </PanelResizeHandle>
  );
}

export {
  ResizablePanelGroup,
  ResizablePanel,
  ResizableHandle,
  PanelGroup as _PanelGroup,
  Panel as _Panel,
  PanelResizeHandle as _PanelResizeHandle,
};
