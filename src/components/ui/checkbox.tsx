'use client';

import * as React from 'react';
import * as CheckboxPrimitive from '@radix-ui/react-checkbox';
import { CheckIcon, MinusIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

type CheckboxProps = React.ComponentProps<typeof CheckboxPrimitive.Root> & {
  /**
   * Visual state for indeterminate checkbox.
   * Control via checked={true|false} and indeterminate={true} for mixed state.
   */
  indeterminate?: boolean;
};

const Checkbox = React.forwardRef<
  React.ElementRef<typeof CheckboxPrimitive.Root>,
  CheckboxProps
>(({ className, indeterminate, ...props }, ref) => {
  const internalRef = React.useRef<HTMLButtonElement | null>(null);

  React.useEffect(() => {
    const el =
      (ref as React.RefObject<HTMLButtonElement>)?.current ??
      internalRef.current;
    if (el) {
      // @ts-expect-error - property exists on HTMLInputElement; <PERSON><PERSON><PERSON> uses button role
      el.indeterminate = !!indeterminate;
      el.setAttribute('data-indeterminate', indeterminate ? 'true' : 'false');
    }
  }, [indeterminate, ref]);

  return (
    <CheckboxPrimitive.Root
      ref={(node) => {
        internalRef.current = node;
        if (typeof ref === 'function') ref(node);
        else if (ref)
          (ref as React.MutableRefObject<HTMLButtonElement | null>).current =
            node;
      }}
      data-slot="checkbox"
      className={cn(
        'peer border-input size-4 shrink-0 rounded-[4px] border shadow-xs',
        'focus-visible:border-ring focus-visible:ring-ring/50 outline-none focus-visible:ring-[3px]',
        'disabled:cursor-not-allowed disabled:opacity-50',
        'data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground',
        'data-[state=unchecked]:bg-background',
        'data-[indeterminate=true]:bg-primary data-[indeterminate=true]:text-primary-foreground',
        className
      )}
      {...props}
    >
      <CheckboxPrimitive.Indicator
        data-slot="checkbox-indicator"
        className="flex items-center justify-center text-current"
      >
        {indeterminate ? (
          <MinusIcon className="size-3.5" />
        ) : (
          <CheckIcon className="size-3.5" />
        )}
      </CheckboxPrimitive.Indicator>
    </CheckboxPrimitive.Root>
  );
});
Checkbox.displayName = 'Checkbox';

export { Checkbox };
