import * as React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';
import { Loader2 } from 'lucide-react';

const spinLoaderVariants = cva('animate-spin', {
  variants: {
    size: {
      xs: 'h-3 w-3',
      sm: 'h-4 w-4',
      md: 'h-5 w-5',
      lg: 'h-6 w-6',
      xl: 'h-8 w-8',
    },
    variant: {
      default: 'text-[hsl(var(--foreground))]',
      primary: 'text-[hsl(var(--primary))]',
      secondary: 'text-[hsl(var(--secondary-foreground))]',
      muted: 'text-[hsl(var(--muted-foreground))]',
      accent: 'text-[hsl(var(--accent-foreground))]',
      destructive: 'text-[hsl(var(--destructive))]',
    },
  },
  defaultVariants: {
    size: 'md',
    variant: 'primary',
  },
});

type SpinLoaderProps = React.HTMLAttributes<HTMLSpanElement> &
  VariantProps<typeof spinLoaderVariants> & {
    label?: string;
    showText?: boolean;
    text?: string;
  };

function SpinLoader({
  className,
  size,
  variant,
  label = 'Loading…',
  showText = false,
  text,
  ...props
}: SpinLoaderProps) {
  return (
    <span
      role="status"
      aria-live="polite"
      data-slot="spin-loader"
      className={cn('inline-flex items-center', showText ? 'gap-2' : '', className)}
      {...props}
    >
      <Loader2
        className={cn(spinLoaderVariants({ size, variant }))}
        aria-hidden="true"
      />
      {showText && (
        <span className="text-sm">
          {text || label}
        </span>
      )}
      <span className="sr-only">{label}</span>
    </span>
  );
}

export { SpinLoader, spinLoaderVariants };