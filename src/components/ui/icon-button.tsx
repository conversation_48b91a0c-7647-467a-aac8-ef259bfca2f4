import * as React from 'react';
import { cn } from '@/lib/utils';
import { Button } from './button';

type IconButtonProps = Omit<
  React.ComponentProps<typeof Button>,
  'children' | 'size'
> & {
  'aria-label': string;
  icon: React.ReactElement;
};

/**
 * Accessible icon-only button composed from Button.
 * - Forces size to "icon"
 * - Requires a non-empty aria-label for screen readers
 */
function IconButton({ icon, className, ...props }: IconButtonProps) {
  if (process.env.NODE_ENV !== 'production') {
    const label = props['aria-label'];
    if (!label || typeof label !== 'string' || label.trim().length === 0) {
      console.warn(
        'IconButton requires a non-empty aria-label for accessibility.'
      );
    }
  }

  return (
    <Button
      data-slot="icon-button"
      size="icon"
      className={cn('p-0', className)}
      {...props}
    >
      {icon}
    </Button>
  );
}

export { IconButton };
