'use client';

import { useEffect } from 'react';
import { useAuthStore } from '@/store/auth';

interface AuthProviderProps {
  children: React.ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const initializeFromJWT = useAuthStore((state) => state.initializeFromJWT);

  useEffect(() => {
    // Initialize auth state from JWT cookie when the app loads
    initializeFromJWT();
  }, [initializeFromJWT]);

  return <>{children}</>;
}