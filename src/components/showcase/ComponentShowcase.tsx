'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { 
  Accordion, 
  AccordionContent, 
  AccordionItem, 
  AccordionTrigger 
} from '@/components/ui/accordion';
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { Spinner } from '@/components/ui/spinner';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { ArrowIcon } from '@/components/icons/ArrowIcon';
import { GoogleIcon } from '@/components/icons/GoogleIcon';
import { ThemeToggle } from '@/components/theme/theme-toggle';
import { CheckIcon, AlertTriangleIcon, InfoIcon } from 'lucide-react';

export function ComponentShowcase() {
  const [switchValue, setSwitchValue] = useState(false);
  const [checkboxValue, setCheckboxValue] = useState(false);
  const [indeterminateValue, setIndeterminateValue] = useState(true);

  // Fix scrolling issue by removing overflow hidden from html and body
  useEffect(() => {
    const html = document.documentElement;
    const body = document.body;
    
    // Store original styles
    const originalHtmlStyle = html.style.overflow;
    const originalBodyStyle = body.style.overflow;
    const originalHtmlHeight = html.style.height;
    const originalBodyHeight = body.style.height;
    
    // Enable scrolling
    html.style.overflow = 'auto';
    body.style.overflow = 'auto';
    html.style.height = 'auto';
    body.style.height = 'auto';
    
    // Cleanup function to restore original styles
    return () => {
      html.style.overflow = originalHtmlStyle;
      body.style.overflow = originalBodyStyle;
      html.style.height = originalHtmlHeight;
      body.style.height = originalBodyHeight;
    };
  }, []);

  return (
    <div className="w-full bg-background min-h-screen">
      <div className="w-full max-w-7xl mx-auto p-6 space-y-8 pb-16">
        {/* Header with Theme Toggle */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-8">
          <div>
            <h1 className="text-4xl font-bold mb-2">Component Showcase</h1>
            <p className="text-muted-foreground text-lg">
              A comprehensive demonstration of all available UI components
            </p>
          </div>
          <div className="flex items-center gap-4">
            <span className="text-sm font-medium">Theme:</span>
            <ThemeToggle />
          </div>
        </div>

        <Tabs defaultValue="buttons" className="w-full">
          <TabsList className="grid w-full grid-cols-2 lg:grid-cols-4">
            <TabsTrigger value="buttons">Buttons & Actions</TabsTrigger>
            <TabsTrigger value="inputs">Form Inputs</TabsTrigger>
            <TabsTrigger value="display">Display</TabsTrigger>
            <TabsTrigger value="feedback">Feedback</TabsTrigger>
          </TabsList>

          {/* Buttons & Actions Tab */}
          <TabsContent value="buttons" className="space-y-8">
            
            {/* Buttons */}
            <Card>
              <CardHeader>
                <CardTitle>Buttons</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <h4 className="text-sm font-medium mb-3">Variants</h4>
                  <div className="flex flex-wrap gap-3">
                    <Button variant="default">Default</Button>
                    <Button variant="destructive">Destructive</Button>
                    <Button variant="outline">Outline</Button>
                    <Button variant="secondary">Secondary</Button>
                    <Button variant="ghost">Ghost</Button>
                    <Button variant="link">Link</Button>
                  </div>
                </div>
                
                <div>
                  <h4 className="text-sm font-medium mb-3">Sizes</h4>
                  <div className="flex flex-wrap gap-3 items-center">
                    <Button size="sm">Small</Button>
                    <Button size="default">Default</Button>
                    <Button size="lg">Large</Button>
                    <Button size="icon">
                      <ArrowIcon className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <div>
                  <h4 className="text-sm font-medium mb-3">States</h4>
                  <div className="flex flex-wrap gap-3">
                    <Button>Normal</Button>
                    <Button disabled>Disabled</Button>
                    <Button isLoading>Loading</Button>
                    <Button isLoading loadingText="Processing...">Processing</Button>
                    <Button block>Full Width</Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Badges */}
            <Card>
              <CardHeader>
                <CardTitle>Badges</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium mb-3">Variants</h4>
                  <div className="flex flex-wrap gap-3">
                    <Badge>Default</Badge>
                    <Badge variant="secondary">Secondary</Badge>
                    <Badge variant="destructive">Destructive</Badge>
                    <Badge variant="outline">Outline</Badge>
                  </div>
                </div>
                
                <div>
                  <h4 className="text-sm font-medium mb-3">With Icons</h4>
                  <div className="flex flex-wrap gap-3">
                    <Badge>
                      <CheckIcon className="w-3 h-3" />
                      Success
                    </Badge>
                    <Badge variant="destructive">
                      <AlertTriangleIcon className="w-3 h-3" />
                      Error
                    </Badge>
                    <Badge variant="outline">
                      <InfoIcon className="w-3 h-3" />
                      Info
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Icons */}
            <Card>
              <CardHeader>
                <CardTitle>Icons</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-4 items-center">
                  <div className="flex items-center gap-2">
                    <ArrowIcon className="h-4 w-4" />
                    <span className="text-sm">Arrow Icon</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <GoogleIcon className="h-4 w-4" />
                    <span className="text-sm">Google Icon</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckIcon className="h-4 w-4" />
                    <span className="text-sm">Check Icon</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <AlertTriangleIcon className="h-4 w-4" />
                    <span className="text-sm">Alert Triangle</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <InfoIcon className="h-4 w-4" />
                    <span className="text-sm">Info Icon</span>
                  </div>
                </div>
              </CardContent>
            </Card>

          </TabsContent>

          {/* Form Inputs Tab */}
          <TabsContent value="inputs" className="space-y-8">
            
            {/* Input Components */}
            <Card>
              <CardHeader>
                <CardTitle>Input Components</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="text-input">Text Input</Label>
                    <Input 
                      id="text-input" 
                      placeholder="Enter text..." 
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="email-input">Email Input</Label>
                    <Input 
                      id="email-input" 
                      type="email" 
                      placeholder="Enter email..." 
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="password-input">Password Input</Label>
                    <Input 
                      id="password-input" 
                      type="password" 
                      placeholder="Enter password..." 
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="disabled-input">Disabled Input</Label>
                    <Input 
                      id="disabled-input" 
                      disabled 
                      placeholder="Disabled input..." 
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Textarea */}
            <Card>
              <CardHeader>
                <CardTitle>Textarea</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="textarea">Message</Label>
                  <Textarea 
                    id="textarea"
                    placeholder="Enter your message..."
                    className="min-h-24"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="textarea-disabled">Disabled Textarea</Label>
                  <Textarea 
                    id="textarea-disabled"
                    disabled
                    placeholder="This textarea is disabled..."
                    className="min-h-16"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Select */}
            <Card>
              <CardHeader>
                <CardTitle>Select Dropdown</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="select-demo">Choose an option</Label>
                  <Select>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select a fruit" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="apple">Apple</SelectItem>
                      <SelectItem value="banana">Banana</SelectItem>
                      <SelectItem value="orange">Orange</SelectItem>
                      <SelectItem value="grape">Grape</SelectItem>
                      <SelectItem value="mango">Mango</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>

            {/* Checkbox & Switch */}
            <Card>
              <CardHeader>
                <CardTitle>Toggle Components</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <h4 className="text-sm font-medium mb-3">Checkboxes</h4>
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <Checkbox 
                        id="checkbox1" 
                        checked={checkboxValue}
                        onCheckedChange={(checked) => setCheckboxValue(!!checked)}
                      />
                      <Label htmlFor="checkbox1">Regular checkbox</Label>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Checkbox 
                        id="checkbox2" 
                        indeterminate={indeterminateValue}
                        onCheckedChange={() => setIndeterminateValue(!indeterminateValue)}
                      />
                      <Label htmlFor="checkbox2">Indeterminate checkbox</Label>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Checkbox id="checkbox3" disabled />
                      <Label htmlFor="checkbox3">Disabled checkbox</Label>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="text-sm font-medium mb-3">Switches</h4>
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <Switch 
                        id="switch1" 
                        checked={switchValue}
                        onCheckedChange={setSwitchValue}
                      />
                      <Label htmlFor="switch1">Toggle switch</Label>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Switch id="switch2" disabled />
                      <Label htmlFor="switch2">Disabled switch</Label>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Labels */}
            <Card>
              <CardHeader>
                <CardTitle>Labels</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div>
                    <Label>Default Label</Label>
                  </div>
                  <div>
                    <Label required>Required Label</Label>
                  </div>
                  <div>
                    <Label muted>Muted Label</Label>
                  </div>
                  <div>
                    <Label size="sm">Small Label</Label>
                  </div>
                  <div>
                    <Label size="lg">Large Label</Label>
                  </div>
                </div>
              </CardContent>
            </Card>

          </TabsContent>

          {/* Display Tab */}
          <TabsContent value="display" className="space-y-8">
            
            {/* Cards */}
            <Card>
              <CardHeader>
                <CardTitle>Cards</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <Card>
                    <CardHeader>
                      <CardTitle>Simple Card</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-muted-foreground">A simple card with content.</p>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardHeader>
                      <CardTitle>Card with Description</CardTitle>
                      <p className="text-muted-foreground text-sm">This card has a description</p>
                    </CardHeader>
                    <CardContent>
                      <p>Card content goes here.</p>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardHeader>
                      <CardTitle>Interactive Card</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="mb-4">This card has interactive elements.</p>
                      <Button size="sm">Action</Button>
                    </CardContent>
                  </Card>
                </div>
              </CardContent>
            </Card>

            {/* Avatars */}
            <Card>
              <CardHeader>
                <CardTitle>Avatars</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-4 items-center">
                  <Avatar>
                    <AvatarImage src="https://github.com/shadcn.png" alt="Avatar" />
                    <AvatarFallback>CN</AvatarFallback>
                  </Avatar>
                  
                  <Avatar className="h-12 w-12">
                    <AvatarImage src="https://github.com/shadcn.png" alt="Large Avatar" />
                    <AvatarFallback>LG</AvatarFallback>
                  </Avatar>
                  
                  <Avatar className="h-6 w-6">
                    <AvatarFallback className="text-xs">SM</AvatarFallback>
                  </Avatar>
                  
                  <Avatar>
                    <AvatarFallback>AB</AvatarFallback>
                  </Avatar>
                </div>
              </CardContent>
            </Card>

            {/* Tabs */}
            <Card>
              <CardHeader>
                <CardTitle>Tabs Example</CardTitle>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="account" className="w-full">
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="account">Account</TabsTrigger>
                    <TabsTrigger value="password">Password</TabsTrigger>
                    <TabsTrigger value="settings">Settings</TabsTrigger>
                  </TabsList>
                  <TabsContent value="account" className="mt-4">
                    <Card>
                      <CardHeader>
                        <CardTitle>Account</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-2">
                        <div className="space-y-1">
                          <Label htmlFor="name">Name</Label>
                          <Input id="name" defaultValue="John Doe" />
                        </div>
                        <div className="space-y-1">
                          <Label htmlFor="username">Username</Label>
                          <Input id="username" defaultValue="@johndoe" />
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>
                  <TabsContent value="password" className="mt-4">
                    <Card>
                      <CardHeader>
                        <CardTitle>Password</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-2">
                        <div className="space-y-1">
                          <Label htmlFor="current">Current password</Label>
                          <Input id="current" type="password" />
                        </div>
                        <div className="space-y-1">
                          <Label htmlFor="new">New password</Label>
                          <Input id="new" type="password" />
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>
                  <TabsContent value="settings" className="mt-4">
                    <Card>
                      <CardHeader>
                        <CardTitle>Settings</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p>Settings content goes here.</p>
                      </CardContent>
                    </Card>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>

            {/* Accordion */}
            <Card>
              <CardHeader>
                <CardTitle>Accordion</CardTitle>
              </CardHeader>
              <CardContent>
                <Accordion type="single" collapsible className="w-full">
                  <AccordionItem value="item-1">
                    <AccordionTrigger>Is it accessible?</AccordionTrigger>
                    <AccordionContent>
                      Yes. It adheres to the WAI-ARIA design pattern and is fully accessible.
                    </AccordionContent>
                  </AccordionItem>
                  <AccordionItem value="item-2">
                    <AccordionTrigger>Is it styled?</AccordionTrigger>
                    <AccordionContent>
                      Yes. It comes with default styles that matches the other components aesthetic.
                    </AccordionContent>
                  </AccordionItem>
                  <AccordionItem value="item-3">
                    <AccordionTrigger>Is it animated?</AccordionTrigger>
                    <AccordionContent>
                      Yes. It includes smooth animations for expand and collapse actions.
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>
              </CardContent>
            </Card>

          </TabsContent>

          {/* Feedback Tab */}
          <TabsContent value="feedback" className="space-y-8">
            
            {/* Alerts */}
            <Card>
              <CardHeader>
                <CardTitle>Alerts</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Alert>
                  <InfoIcon className="h-4 w-4" />
                  <AlertTitle>Info</AlertTitle>
                  <AlertDescription>
                    This is a default informational alert.
                  </AlertDescription>
                </Alert>
                
                <Alert variant="destructive">
                  <AlertTriangleIcon className="h-4 w-4" />
                  <AlertTitle>Error</AlertTitle>
                  <AlertDescription>
                    This is a destructive alert indicating an error.
                  </AlertDescription>
                </Alert>
                
                <Alert variant="success">
                  <CheckIcon className="h-4 w-4" />
                  <AlertTitle>Success</AlertTitle>
                  <AlertDescription>
                    This is a success alert indicating completion.
                  </AlertDescription>
                </Alert>
                
                <Alert variant="warning">
                  <AlertTriangleIcon className="h-4 w-4" />
                  <AlertTitle>Warning</AlertTitle>
                  <AlertDescription>
                    This is a warning alert indicating caution.
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>

            {/* Spinners */}
            <Card>
              <CardHeader>
                <CardTitle>Spinners</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <h4 className="text-sm font-medium mb-3">Sizes</h4>
                  <div className="flex items-center gap-4">
                    <Spinner size="xs" />
                    <Spinner size="sm" />
                    <Spinner size="md" />
                    <Spinner size="lg" />
                  </div>
                </div>
                
                <div>
                  <h4 className="text-sm font-medium mb-3">Variants</h4>
                  <div className="flex items-center gap-4">
                    <Spinner variant="default" />
                    <Spinner variant="primary" />
                    <Spinner variant="secondary" />
                    <Spinner variant="muted" />
                    <Spinner variant="destructive" />
                  </div>
                </div>
              </CardContent>
            </Card>

          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}