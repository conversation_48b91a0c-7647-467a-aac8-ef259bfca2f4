'use client';

import { useEffect } from 'react';

export function HydrationFix() {
  useEffect(() => {
    // Fix hydration issues caused by browser extensions
    const fixHydrationIssues = () => {
      try {
        const body = document.body;
        if (body) {
          // Remove attributes added by browser extensions that cause hydration mismatches
          const problematicAttributes = [
            'data-demoway-document-id',
            'data-new-gr-c-s-check-loaded',
            'data-gr-ext-installed',
            'data-grammarly-shadow-root',
          ];

          problematicAttributes.forEach((attr) => {
            if (body.hasAttribute(attr)) {
              body.removeAttribute(attr);
            }
          });
        }
      } catch (error) {
        // Silently ignore errors
        console.debug('Hydration fix error:', error);
      }
    };

    // Run immediately
    fixHydrationIssues();

    // Run after a short delay to catch extensions that modify DOM later
    const timeoutId = setTimeout(fixHydrationIssues, 100);

    return () => {
      clearTimeout(timeoutId);
    };
  }, []);

  return null;
}
