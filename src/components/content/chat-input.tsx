'use client';

import React, { useEffect, useMemo, useRef, useState } from 'react';
import Image from 'next/image';
import { Plus, ArrowUp, Square } from 'lucide-react';

type AttachedFile = {
  id: string;
  file: File;
  name: string;
  size: number;
  type: string;
  previewUrl?: string;
};

// Hook to detect mobile screen size
function useIsMobile() {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkIsMobile();
    window.addEventListener('resize', checkIsMobile);
    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  return isMobile;
}


type ChatInputProps = {
  value: string;
  onChange: (value: string) => void;
  onSend?: (value: string, files: File[]) => void;
  disabled?: boolean;
  isLoading?: boolean;
  maxLength?: number;
  onFilesChange?: (count: number) => void;
  variant?: 'landing' | 'inline';
  size?: 'md' | 'sm';
  className?: string;
};

export function ChatInput({
  value,
  onChange,
  onSend,
  disabled = false,
  isLoading = false,
  maxLength = 8000,
  onFilesChange,
  variant = 'landing',
  size = 'md',
  className,
}: ChatInputProps) {
  const isMobile = useIsMobile();
  const textareaRef = useRef<HTMLTextAreaElement | null>(null);
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const [files, setFiles] = useState<AttachedFile[]>([]);

  const sizeClasses = size === 'sm' ? 'text-sm' : 'text-base';
  const remaining = useMemo(
    () => Math.max(0, maxLength - value.length),
    [value.length, maxLength]
  );

  const formatSize = (bytes: number) => {
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${Math.round(bytes / 102.4) / 10} KB`;
    return `${Math.round(bytes / 104857.6) / 10} MB`;
  };

  const fileExt = (name: string) =>
    (name.includes('.') ? name.split('.').pop() || '' : '').toUpperCase();

  useEffect(() => {
    return () => {
      files.forEach((f) => {
        if (f.previewUrl) URL.revokeObjectURL(f.previewUrl);
      });
    };
  }, [files]);

  useEffect(() => {
    onFilesChange?.(files.length);
  }, [files.length, onFilesChange]);

  useEffect(() => {
    const el = textareaRef.current;
    if (!el) return;
    const { selectionStart } = el;
    const lineHeight = 24;
    const caretLine = Math.floor((selectionStart || 0) / 80);
    const targetScroll =
      caretLine * lineHeight - (el.clientHeight - lineHeight * 2);
    if (targetScroll > el.scrollTop) el.scrollTop = targetScroll;
  }, [value]);

  const handleSend = () => {
    if (disabled || isLoading) return;
    if (!value.trim() && files.length === 0) return;
    onSend?.(value, files.map(f => f.file));
  };

  const addFiles = (incoming: FileList | File[]) => {
    Array.from(incoming).forEach((file) => {
      const attachedFile: AttachedFile = {
        id: crypto.randomUUID(),
        file,
        name: file.name,
        size: file.size,
        type: file.type,
        previewUrl: file.type.startsWith('image/') ? URL.createObjectURL(file) : undefined
      };
      setFiles(prev => [...prev, attachedFile]);
    });
  };

  // Inline compact composer (used inside chat view)
  if (variant === 'inline') {
    return (
      <div
        className={className}
        onDragOver={(e) => {
          e.preventDefault();
        }}
        onDrop={(e) => {
          e.preventDefault();
          if (e.dataTransfer?.files?.length) addFiles(e.dataTransfer.files);
        }}
      >
        <div className="w-full max-w-none px-3 bg-background rounded-[10px] shadow-[0px_2px_4px_-1px_rgba(0,0,0,0.08)] dark:shadow-[0px_2px_4px_-1px_rgba(0,0,0,0.3)] outline outline-offset-[-1px] outline-[hsl(var(--primary))]/50 flex flex-col relative">
          <div className="py-2">
            <textarea
              ref={textareaRef}
              value={value}
              onChange={(e) => {
                const next = e.target.value.slice(0, maxLength);
                onChange(next);
              }}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  handleSend();
                }
              }}
              onPaste={(e) => {
                if (e.clipboardData?.files?.length)
                  addFiles(e.clipboardData.files);
              }}
              placeholder="How can i help you"
              className={`pr-12 w-full font-normal text-foreground ${sizeClasses} font-['Inter'overflow-y-auto leading-6 bg-transparent outline-none resize-none ]`}
              style={{
                minHeight: '24px',
                maxHeight: '144px',
                height: 'auto',
              }}
              rows={1}
              disabled={disabled || isLoading}
              onInput={(e) => {
                const target = e.target as HTMLTextAreaElement;
                target.style.height = 'auto';
                target.style.height = `${Math.min(target.scrollHeight, 144)}px`;
              }}
            />
          </div>

          <div className="flex justify-between items-center px-0 pb-2">
            <div className="flex gap-2 items-center">
              <button
                type="button"
                disabled={disabled || isLoading}
                onClick={() => fileInputRef.current?.click()}
                className="flex justify-center items-center bg-white rounded-3xl border border-gray-200 transition-colors cursor-pointer size-6 dark:bg-gray-800 dark:border-gray-600 disabled:opacity-50 hover:bg-gray-50 dark:hover:bg-gray-700"
                aria-label="Attach files"
              >
                <Plus className="w-3 h-3 text-gray-600 dark:text-gray-400" strokeWidth={2} />
              </button>
              <input
                ref={fileInputRef}
                type="file"
                multiple
                className="hidden"
                onChange={(e) => {
                  if (e.target.files?.length) addFiles(e.target.files);
                  if (fileInputRef.current) fileInputRef.current.value = '';
                }}
              />
            </div>
            <div className="flex gap-2 items-center">
              <span className="text-[11px] text-muted-foreground select-none">{remaining}</span>
              {isLoading ? (
                <button
                  type="button"
                  onClick={() => { /* noop */ }}
                  className="overflow-hidden relative rounded-3xl bg-muted-foreground size-6"
                  aria-label="Stop"
                  disabled
                >
                  <div className="size-4 left-[4px] top-[4px] absolute overflow-hidden">
                    <Square className="w-[9.33px] h-3 left-[3.33px] top-[2px] absolute text-background" strokeWidth={2} />
                  </div>
                </button>
              ) : (
                <button
                  type="button"
                  onClick={handleSend}
                  className={`size-6 relative rounded-3xl overflow-hidden transition-colors disabled:opacity-50 cursor-pointer ${
                    value.trim() || files.length 
                      ? 'bg-[hsl(var(--primary))] hover:bg-[hsl(var(--primary))]/90' 
                      : 'bg-gray-300 dark:bg-gray-600'
                  }`}
                  aria-label="Send"
                  disabled={disabled || (!value.trim() && files.length === 0)}
                >
                  <div className="absolute top-[4px] left-[4px] size-4 overflow-hidden">
                    <ArrowUp
                      className="absolute top-[2px] left-[3.33px] h-3 w-[9.33px] text-white"
                      strokeWidth={2}
                    />
                  </div>
                </button>
              )}
            </div>
          </div>

          {files.length > 0 && (
            <div className="px-3 pb-2">
              <div className="mb-2 h-px bg-border" />
              <div className="flex flex-wrap gap-2 min-h-[58px]">
                {files.map((file, idx) => (
                  <div key={`${file.name}-${idx}`} className="relative shrink-0 w-[82px]">
                    <div className="w-[82px] h-[62px] bg-muted rounded-md overflow-hidden flex items-center justify-center">
                      {file.previewUrl ? (
                        <Image src={file.previewUrl} alt={file.name} className="object-cover w-full h-full" width={82} height={62} />
                      ) : (
                        <div className="flex flex-col justify-center items-center text-muted-foreground">
                          <span className="text-[11px]">{fileExt(file.name) || 'FILE'}</span>
                          <span className="text-[10px]">{formatSize(file.size)}</span>
                        </div>
                      )}
                    </div>
                    <div className="mt-1 text-[11px] text-foreground truncate" title={file.name}>{file.name}</div>
                    <button
                      type="button"
                      aria-label={`Remove ${file.name}`}
                      className="absolute -top-1 -right-1 w-5 h-5 rounded-full bg-background outline outline-offset-[-1px] outline-border text-muted-foreground hover:text-foreground flex items-center justify-center cursor-pointer"
                      onClick={() => setFiles((prev) => prev.filter((_, i) => i !== idx))}
                    >
                      <span className="leading-none">×</span>
                    </button>
                  </div>
                ))}
                <button
                  type="button"
                  aria-label="Clear all attachments"
                  className="shrink-0 h-[62px] px-2 rounded-md bg-muted text-[11px] text-muted-foreground hover:text-foreground"
                  onClick={() => setFiles([])}
                >
                  Clear all
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Main chat input container - responsive positioning */}
      <div
        className="absolute"
        style={
          isMobile
            ? {
                left: '16px',
                right: '16px',
                top: '160px',
              }
            : {
                left: '50%',
                top: '238px',
                transform: 'translateX(-50%)',
              }
        }
        onDragOver={(e) => {
          e.preventDefault();
        }}
        onDrop={(e) => {
          e.preventDefault();
          if (e.dataTransfer?.files?.length) addFiles(e.dataTransfer.files);
        }}
      >
        <div 
          className="px-3 pt-2.5 pb-9 bg-background rounded-[10px] shadow-[0px_2px_4px_-1px_rgba(0,0,0,0.08)] dark:shadow-[0px_2px_4px_-1px_rgba(0,0,0,0.3)] outline outline-offset-[-1px] outline-[hsl(var(--primary))]/50 inline-flex flex-col justify-start items-center overflow-hidden relative"
          style={{
            width: isMobile ? '100%' : '616px',
            height: files.length > 0 ? '236px' : '156px',
          }}
        >
          <div className="inline-flex relative flex-1 justify-between items-start self-stretch w-full">
            <textarea
              ref={textareaRef}
              value={value}
              onChange={(e) => {
                const next = e.target.value.slice(0, maxLength);
                onChange(next);
              }}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  handleSend();
                }
              }}
              onPaste={(e) => {
                if (e.clipboardData?.files?.length)
                  addFiles(e.clipboardData.files);
              }}
              placeholder="How can i help you"
              className={`absolute left-0 w-full pr-10 pb-2 text-foreground ${sizeClasses} font-normal font-['Inter'] leading-[23.10px] bg-transparent outline-none overflow-auto resize-none`}
              style={{ 
                top: 0, 
                bottom: files.length > 0 ? 92 : 1,
                fontSize: isMobile ? '16px' : undefined,
                lineHeight: isMobile ? '1.4' : undefined,
              }}
              disabled={disabled || isLoading}
            />
          </div>

          {/* Bottom area: actions row above preview tiles */}
          <div className="absolute bottom-2 right-3 left-3">
            <div className="flex justify-between items-center">
              <div className="flex gap-2 items-center">
                <button
                  type="button"
                  disabled={disabled || isLoading}
                  onClick={() => fileInputRef.current?.click()}
                  className="flex justify-center items-center bg-white rounded-3xl border border-gray-200 transition-colors cursor-pointer size-6 dark:bg-gray-800 dark:border-gray-600 disabled:opacity-50 hover:bg-gray-50 dark:hover:bg-gray-700"
                  aria-label="Attach files"
                >
                  <Plus className="w-3 h-3 text-gray-600 dark:text-gray-400" strokeWidth={2} />
                </button>
                <input
                  ref={fileInputRef}
                  type="file"
                  multiple
                  className="hidden"
                  onChange={(e) => {
                    if (e.target.files?.length) addFiles(e.target.files);
                    if (fileInputRef.current) fileInputRef.current.value = '';
                  }}
                />
              </div>
              <div className="flex gap-2 items-center">
                <span className="text-[11px] text-muted-foreground select-none">{remaining}</span>
                {isLoading ? (
                  <button
                    type="button"
                    onClick={() => {/* noop: parent can control isLoading to stop */}}
                    className="overflow-hidden relative rounded-3xl bg-muted-foreground size-6"
                    aria-label="Stop"
                    disabled
                  >
                    <div className="size-4 left-[4px] top-[4px] absolute overflow-hidden">
                      <Square className="w-[9.33px] h-3 left-[3.33px] top-[2px] absolute text-background" strokeWidth={2} />
                    </div>
                  </button>
                ) : (
                  <button
                    type="button"
                    onClick={handleSend}
                    className={`size-6 relative rounded-3xl overflow-hidden transition-colors disabled:opacity-50 cursor-pointer ${
                      value.trim() || files.length 
                        ? 'bg-[hsl(var(--primary))] hover:bg-[hsl(var(--primary))]/90' 
                        : 'bg-gray-300 dark:bg-gray-600'
                    }`}
                    aria-label="Send"
                    disabled={disabled || (!value.trim() && files.length === 0)}
                  >
                    <div className="absolute top-[4px] left-[4px] size-4 overflow-hidden">
                      <ArrowUp
                        className="absolute top-[2px] left-[3.33px] h-3 w-[9.33px] text-white"
                        strokeWidth={2}
                      />
                    </div>
                  </button>
                )}
              </div>
            </div>

            {files.length > 0 && (
              <div className="mt-2" style={{ minHeight: 80 }}>
                <div className="mb-2 h-px bg-border" />
                <div className="flex flex-wrap gap-2 min-h-[58px]">
                  {files.map((file, idx) => (
                    <div key={`${file.name}-${idx}`} className="relative shrink-0 w-[82px]">
                      <div className="w-[82px] h-[62px] bg-muted rounded-md overflow-hidden flex items-center justify-center">
                        {file.previewUrl ? (
                          <Image src={file.previewUrl} alt={file.name} className="object-cover w-full h-full" width={82} height={62} />
                        ) : (
                          <div className="flex flex-col justify-center items-center text-muted-foreground">
                            <span className="text-[11px]">{fileExt(file.name) || 'FILE'}</span>
                            <span className="text-[10px]">{formatSize(file.size)}</span>
                          </div>
                        )}
                      </div>
                      <div className="mt-1 text-[11px] text-foreground truncate" title={file.name}>{file.name}</div>
                      <button
                        type="button"
                        aria-label={`Remove ${file.name}`}
                        className="absolute -top-1 -right-1 w-5 h-5 rounded-full bg-background outline outline-offset-[-1px] outline-border text-muted-foreground hover:text-foreground flex items-center justify-center cursor-pointer"
                        onClick={() => setFiles((prev) => prev.filter((_, i) => i !== idx))}
                      >
                        <span className="leading-none">×</span>
                      </button>
                    </div>
                  ))}
                  <button
                    type="button"
                    aria-label="Clear all attachments"
                    className="shrink-0 h-[62px] px-2 rounded-md bg-muted text-[11px] text-muted-foreground hover:text-foreground"
                    onClick={() => setFiles([])}
                  >
                    Clear all
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
}

export default ChatInput;
