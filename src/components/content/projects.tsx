'use client';

import React, {
  useState,
  useCallback,
  useRef,
  useEffect,
} from 'react';
import { MoreVertical, Edit3, Trash2, FolderO<PERSON>, AlertCircle, Folder } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { SpinLoader } from '@/components/ui/spin-loader';
import { useListProjects } from '@/hooks/useNode';
import { Project } from '@/types/node';

// Types
interface ProjectsProps {
  onProjectClick?: (projectId: string) => void;
  onNewProject?: () => void;
  className?: string;
  tenantId?: string;
}

// Helper function to format date
const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const diffTime = Math.abs(now.getTime() - date.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays === 1) {
    return 'Yesterday';
  } else if (diffDays <= 7) {
    return `${diffDays} days ago`;
  } else {
    return `Updated ${diffDays} days ago`;
  }
};

export function Projects({
  onProjectClick,
  onNewProject,
  className = '',
  tenantId,
}: ProjectsProps) {
  // State management
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [openMenuId, setOpenMenuId] = useState<string | null>(null);
  const menuRef = useRef<HTMLDivElement>(null);

  // API call using useListProjects hook
  const {
    data: projectsData,
    isLoading,
    isError,
    error,
    refetch
  } = useListProjects({
    page: currentPage,
    page_size: 12,
    search: searchQuery.trim() || undefined,
    tenant_id: tenantId,
  });

  const projects = projectsData?.data?.projects || [];
  const pagination = projectsData?.data?.pagination;
  const hasMoreProjects = pagination?.has_next || false;

  // Close menu when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setOpenMenuId(null);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Event handlers
  const handleProjectClick = useCallback(
    (projectId: string) => {
      onProjectClick?.(projectId);
    },
    [onProjectClick]
  );

  const handleNewProject = useCallback(() => {
    onNewProject?.();
  }, [onNewProject]);

  const handleSearchChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setSearchQuery(e.target.value);
      setCurrentPage(1); // Reset to first page when searching
    },
    []
  );

  const handleShowMore = useCallback(() => {
    if (hasMoreProjects && !isLoading) {
      setCurrentPage((prev) => prev + 1);
    }
  }, [hasMoreProjects, isLoading]);

  const handleMenuClick = useCallback(
    (projectId: string, e: React.MouseEvent) => {
      e.stopPropagation();
      setOpenMenuId(openMenuId === projectId ? null : projectId);
    },
    [openMenuId]
  );

  const handleRename = useCallback((projectId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setOpenMenuId(null);
    // Handle rename logic here
    console.log('Rename project:', projectId);
    // In production, you might show a rename modal or inline edit
  }, []);

  const handleDelete = useCallback((projectId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setOpenMenuId(null);
    // Handle delete logic here
    console.log('Delete project:', projectId);
    // In production, you might show a confirmation dialog
  }, []);

  // Error state
  if (isError) {
    return (
      <div className={`relative flex h-full flex-col bg-white ${className}`}>
        <div className="flex flex-1 items-center justify-center">
          <div className="mx-auto max-w-md px-4 text-center">
            <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-red-100">
              <AlertCircle className="h-8 w-8 text-red-400" />
            </div>
            <h3 className="mb-2 text-lg font-medium text-[hsl(var(--foreground))]">
              Failed to load projects
            </h3>
            <p className="mb-6 text-[hsl(var(--foreground))] opacity-80">
              {error?.message || 'Something went wrong while fetching projects'}
            </p>
            <Button
              onClick={() => refetch()}
              className="bg-[hsl(var(--primary))] text-white hover:bg-[hsl(var(--primary))] hover:opacity-90"
            >
              Try Again
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative flex h-full flex-col bg-background ${className}`}>
      {/* Decorative background element */}
      <div className="pointer-events-none absolute top-0 right-0 h-[302px] w-[446px] overflow-hidden rounded-full bg-[hsl(var(--primary))] opacity-40 blur-[240px]" />

      {/* Header - Fixed */}
      <div className="relative flex-shrink-0 border-b border-border">
        <div className="mx-auto flex max-w-[1380px] items-center justify-between px-4 py-6 sm:px-8">
          <h1 className="text-xl leading-[150%] font-semibold text-[hsl(var(--foreground))] sm:text-2xl">
            All Projects
          </h1>

          <div className="flex items-center gap-2 sm:gap-4">
            {/* Search Input */}
            <div className="relative">
              <Input
                value={searchQuery}
                onChange={handleSearchChange}
                placeholder="Search projects..."
                className="h-[38px] w-48 rounded-lg border-[hsl(var(--border))] bg-[hsl(var(--muted))] px-4 text-sm transition-colors placeholder:text-[hsl(var(--foreground))] placeholder:opacity-70 focus:border-[hsl(var(--primary))] focus:ring-[hsl(var(--primary))] sm:w-64"
                aria-label="Search projects"
              />
            </div>

            {/* New Project Button */}
            <Button
              onClick={handleNewProject}
              className="h-[38px] rounded-md bg-[hsl(var(--primary))] px-3 text-sm font-medium text-white transition-colors hover:bg-[hsl(var(--primary))] hover:opacity-90 sm:px-4"
              aria-label="Create new project"
            >
              <span className="hidden sm:inline">New Project</span>
              <span className="sm:hidden">New</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Search Results Info - Fixed */}
      {searchQuery && !isLoading && (
        <div className="relative flex-shrink-0 border-b border-gray-50">
          <div className="mx-auto max-w-[1380px] px-4 py-3 sm:px-8">
            <p className="text-sm text-[hsl(var(--foreground))] opacity-70">
              {projects.length === 0
                ? `No projects found for "${searchQuery}"`
                : `${pagination?.total_count || projects.length} project${projects.length === 1 ? '' : 's'} found`}
            </p>
          </div>
        </div>
      )}

      {/* Projects List - Scrollable */}
      <div className="flex-1 overflow-y-auto">
        <div className="mx-auto max-w-[1380px]">
          {isLoading && currentPage === 1 ? (
            // Loading state for first page
            <div className="flex items-center justify-center py-20">
              <div className="text-center">
                <div className="mx-auto mb-4">
                  <SpinLoader size="xl" variant="primary" />
                </div>
                <p className="text-[hsl(var(--foreground))] opacity-80">Loading projects...</p>
              </div>
            </div>
          ) : projects.length > 0 ? (
            <div className="px-4 py-6 sm:px-8">
              <div className="divide-y divide-[hsl(var(--border))]">
                {projects.map((project: Project) => (
                  <div
                    key={project.id}
                    className="group -mx-4 flex cursor-pointer items-start gap-4 px-4 py-5 transition-all duration-200 hover:bg-accent"
                    onClick={() => handleProjectClick(project.id.toString())}
                    role="button"
                    tabIndex={0}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        handleProjectClick(project.id.toString());
                      }
                    }}
                    aria-label={`Open project: ${project.Title}`}
                  >
                    {/* Project Icon */}
                    <div className="flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-lg bg-[hsl(var(--muted))] transition-all duration-200 group-hover:bg-[hsl(var(--primary))]/8">
                      <Folder className="h-6 w-6 text-[hsl(var(--primary))]" />
                    </div>

                    {/* Content */}
                    <div className="min-w-0 flex-1">
                      {/* Title and Menu */}
                      <div className="mb-1 flex items-start justify-between">
                        <h3 className="truncate pr-2 text-base leading-[164.99%] font-medium text-[hsl(var(--foreground))] transition-colors group-hover:text-[hsl(var(--primary))]">
                          {project.Title}
                        </h3>

                        {/* Menu Button */}
                        <div
                          className="relative"
                          ref={openMenuId === project.id.toString() ? menuRef : null}
                        >
                          <button
                            onClick={(e) => handleMenuClick(project.id.toString(), e)}
                            className="flex h-[32px] w-[32px] flex-shrink-0 cursor-pointer items-center justify-center rounded-lg border border-gray-100/50 bg-white/90 opacity-0 backdrop-blur-sm transition-all duration-200 group-hover:opacity-100 hover:bg-white hover:shadow-sm"
                            aria-label={`More options for ${project.Title}`}
                          >
                            <MoreVertical className="h-4 w-4 text-[hsl(var(--muted-foreground))]" />
                          </button>

                          {/* Dropdown Menu */}
                          {openMenuId === project.id.toString() && (
                            <div className="animate-in fade-in-0 zoom-in-95 absolute top-10 right-0 z-50 w-44 rounded-xl border border-gray-200 bg-white py-2 shadow-lg duration-100">
                              <button
                                onClick={(e) => handleRename(project.id.toString(), e)}
                                className="flex w-full cursor-pointer items-center gap-3 px-3 py-2.5 text-sm text-[hsl(var(--foreground))] transition-colors hover:bg-gray-50"
                              >
                                <Edit3 className="h-4 w-4" />
                                Rename
                              </button>
                              <button
                                onClick={(e) => handleDelete(project.id.toString(), e)}
                                className="flex w-full cursor-pointer items-center gap-3 px-3 py-2.5 text-sm text-red-600 transition-colors hover:bg-red-50"
                              >
                                <Trash2 className="h-4 w-4" />
                                Delete
                              </button>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Timestamp and Creator */}
                      <div className="mb-2">
                        <span className="text-sm leading-[164.99%] text-[hsl(var(--foreground))] opacity-80">
                          {formatDate(project.created_at)}
                        </span>
                      </div>

                      {/* Creator Info */}
                      <div className="mb-2">
                        <p className="text-sm leading-[164.99%] text-[hsl(var(--foreground))] opacity-90">
                          Created by <span className="font-medium">{project.creator_name || project.creator_email}</span>
                        </p>
                      </div>

                      {/* Project ID Tag */}
                      <div className="flex flex-wrap gap-1">
                        <span className="inline-flex items-center rounded-md bg-blue-100 px-2 py-1 text-xs font-medium text-blue-700">
                          ID: {project.id}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}

                {/* Show More Button */}
                {hasMoreProjects && (
                  <div className="flex justify-center pt-8 pb-6">
                    <Button
                      onClick={handleShowMore}
                      disabled={isLoading}
                      variant="outline"
                      className="min-w-[200px] cursor-pointer border-[hsl(var(--border))] text-[hsl(var(--foreground))] hover:border-[hsl(var(--primary))] hover:text-[hsl(var(--primary))] disabled:cursor-not-allowed disabled:opacity-50"
                    >
                      {isLoading ? (
                        <SpinLoader size="sm" showText text="Loading more projects..." />
                      ) : (
                        'Show More'
                      )}
                    </Button>
                  </div>
                )}
              </div>
            </div>
          ) : (
            /* Empty State */
            <div className="flex flex-1 items-center justify-center py-20">
              <div className="mx-auto max-w-md px-4 text-center">
                <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-muted">
                  <FolderOpen className="h-8 w-8 text-muted-foreground" />
                </div>
                <h3 className="mb-2 text-lg font-medium text-[hsl(var(--foreground))]">
                  {searchQuery ? 'No projects found' : 'No projects yet'}
                </h3>
                <p className="mb-6 text-[hsl(var(--foreground))] opacity-80">
                  {searchQuery
                    ? `Try adjusting your search term "${searchQuery}"`
                    : 'Create your first project to get started'}
                </p>
                {!searchQuery && (
                  <Button
                    onClick={handleNewProject}
                    className="bg-[hsl(var(--primary))] text-white hover:bg-[hsl(var(--primary))] hover:opacity-90"
                  >
                    Create New Project
                  </Button>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
