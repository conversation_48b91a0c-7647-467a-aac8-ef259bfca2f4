'use client';

import React, {
  useState,
  useMemo,
  useCallback,
  useRef,
  useEffect,
} from 'react';
import { MoreVertical, Edit3, Trash2, <PERSON><PERSON><PERSON><PERSON>, AlertTriangle, MessageSquare } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Skeleton } from '@/components/ui/skeleton';
import { SpinLoader } from '@/components/ui/spin-loader';

// Types
interface ProjectChatItem {
  id: string;
  title: string;
  description: string;
  timestamp: string;
  emoji: string | React.ReactNode;
  createdAt: Date;
  messageCount: number;
}

interface ProjectChatHistoryProps {
  projectId?: string;
  projectName: string;
  projectDescription: string;
  projectTechStack: string[];
  pastTasks?: ProjectChatItem[];
  isTasksLoading?: boolean;
  isProjectLoading?: boolean;
  tasksError?: Error | null;
  onChatClick?: (chatId: string) => void;
  onNewChat?: () => void;
  onBackToProjects?: () => void;
  className?: string;
}


export function ProjectChatHistory({
  projectId: _projectId, // eslint-disable-line @typescript-eslint/no-unused-vars
  projectName,
  projectDescription,
  projectTechStack,
  pastTasks,
  isTasksLoading = false,
  isProjectLoading = false,
  tasksError,
  onChatClick,
  onNewChat,
  onBackToProjects,
  className = '',
}: ProjectChatHistoryProps) {
  // Note: _projectId is available for future use when implementing real API calls
  // State management
  const [searchQuery, setSearchQuery] = useState('');
  const [visibleCount, setVisibleCount] = useState(6);
  const [isLoading, setIsLoading] = useState(false);
  
  const [openMenuId, setOpenMenuId] = useState<string | null>(null);
  const menuRef = useRef<HTMLDivElement>(null);

  // Memoized filtered chats based on search query
  const filteredChats = useMemo(() => {
    // Use only real data from API - no mock data fallback
    const allChats = pastTasks || [];
    
    if (!searchQuery.trim()) return allChats;

    const query = searchQuery.toLowerCase();
    return allChats.filter(
      (chat) =>
        chat.title.toLowerCase().includes(query) ||
        chat.description.toLowerCase().includes(query)
    );
  }, [pastTasks, searchQuery]);

  // Visible chats based on pagination
  const visibleChats = useMemo(
    () => filteredChats.slice(0, visibleCount),
    [filteredChats, visibleCount]
  );

  const hasMoreChats = filteredChats.length > visibleCount;

  // Close menu when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setOpenMenuId(null);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [setOpenMenuId]);

  // Event handlers
  const handleChatClick = useCallback(
    (chatId: string) => {
      onChatClick?.(chatId);
    },
    [onChatClick]
  );

  const handleNewChat = useCallback(() => {
    onNewChat?.();
  }, [onNewChat]);

  const handleBackToProjects = useCallback(() => {
    onBackToProjects?.();
  }, [onBackToProjects]);

  const handleSearchChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setSearchQuery(e.target.value);
      setVisibleCount(6); // Reset pagination when searching
    },
    []
  );

  const handleShowMore = useCallback(async () => {
    setIsLoading(true);

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 800));

    setVisibleCount((prev) => prev + 6);
    setIsLoading(false);
  }, [setVisibleCount, setIsLoading]);

  const handleMenuClick = useCallback(
    (chatId: string, e: React.MouseEvent) => {
      e.stopPropagation();
      setOpenMenuId(openMenuId === chatId ? null : chatId);
    },
    [openMenuId]
  );

  const handleRename = useCallback((chatId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setOpenMenuId(null);
    // Handle rename logic here
    console.log('Rename chat:', chatId);
    // In production, you might show a rename modal or inline edit
  }, [setOpenMenuId]);

  const handleDelete = useCallback((chatId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setOpenMenuId(null);
    // Handle delete logic here
    console.log('Delete chat:', chatId);
    // In production, you might show a confirmation dialog
  }, [setOpenMenuId]);

  return (
    <div className={`relative flex h-full flex-col bg-white ${className}`}>
      {/* Decorative background element */}
      <div className="pointer-events-none absolute top-0 right-0 h-[302px] w-[446px] overflow-hidden rounded-full bg-[hsl(var(--primary))] opacity-40 blur-[240px]" />

      {/* Header - Fixed */}
      <div className="relative flex-shrink-0 border-b border-border">
        <div className="px-4 py-6 sm:px-8">
          {/* Top row with back button and controls */}
          <div className="mb-4 flex items-center justify-between">
            {/* Back Button */}
            <Button
              onClick={handleBackToProjects}
              variant="ghost"
              size="sm"
              className="flex items-center gap-2 p-2 text-[hsl(var(--foreground))] hover:bg-[hsl(var(--primary))]/8 hover:text-[hsl(var(--primary))]"
            >
              <ArrowLeft className="h-4 w-4" />
              <span className="hidden sm:inline">Back to Projects</span>
            </Button>

            {/* Controls on the right */}
            <div className="flex items-center gap-2 sm:gap-4">
              {/* Search Input */}
              <div className="relative">
                <Input
                  value={searchQuery}
                  onChange={handleSearchChange}
                  placeholder={`Search in ${projectName}...`}
                  className="h-[38px] w-48 rounded-lg border-[hsl(var(--border))] bg-[hsl(var(--muted))] px-4 text-sm transition-colors placeholder:text-[hsl(var(--foreground))] placeholder:opacity-70 focus:border-[hsl(var(--primary))] focus:ring-[hsl(var(--primary))] sm:w-64"
                  aria-label="Search project chats"
                />
              </div>

              {/* New Chat Button */}
              <Button
                onClick={handleNewChat}
                className="h-[38px] rounded-md bg-[hsl(var(--primary))] px-3 text-sm font-medium text-white transition-colors hover:bg-[hsl(var(--primary))] hover:opacity-90 sm:px-4"
                aria-label="Start new chat in project"
              >
                <span className="hidden sm:inline">New Chat in Project</span>
                <span className="sm:hidden">New Chat</span>
              </Button>
            </div>
          </div>

          {/* Project Information - Aligned with chat list */}
          <div className="mx-auto max-w-[1380px]">
            {isProjectLoading ? (
              <>
                <Skeleton className="mb-2 h-8 w-64" withShimmer />
                <Skeleton className="mb-3 h-4 w-full" withShimmer />
                <div className="flex flex-wrap gap-1">
                  <Skeleton className="h-6 w-16" withShimmer />
                  <Skeleton className="h-6 w-20" withShimmer />
                </div>
              </>
            ) : (
              <>
                <h1 className="mb-2 text-2xl leading-[150%] font-semibold text-[hsl(var(--foreground))]">
                  {projectName}
                </h1>
                <p className="mb-3 text-sm text-[hsl(var(--foreground))] opacity-70">
                  {projectDescription}
                </p>

                {/* Technology Chips */}
                <div className="flex flex-wrap gap-1">
                  {projectTechStack.map((tech, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center rounded-md bg-muted px-2 py-1 text-xs font-medium text-muted-foreground"
                    >
                      {tech}
                    </span>
                  ))}
                </div>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Search Results Info - Fixed */}
      {searchQuery && (
        <div className="relative flex-shrink-0 border-b border-gray-50">
          <div className="mx-auto max-w-[1380px] px-4 py-3 sm:px-8">
            <p className="text-sm text-[hsl(var(--foreground))] opacity-70">
              {filteredChats.length === 0
                ? `No chats found for "${searchQuery}"`
                : `${filteredChats.length} chat${filteredChats.length === 1 ? '' : 's'} found`}
            </p>
          </div>
        </div>
      )}

      {/* Chat List - Scrollable */}
      <div className="flex-1 overflow-y-auto">
        <div className="mx-auto max-w-[1380px]">
          {/* Loading state */}
          {isTasksLoading ? (
            <div className="flex flex-1 items-center justify-center py-20">
              <div className="mx-auto max-w-md px-4 text-center">
                <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center">
                  <SpinLoader size="xl" variant="primary" />
                </div>
                <h3 className="mb-2 text-lg font-medium text-[hsl(var(--foreground))]">
                  Loading past tasks...
                </h3>
                <p className="text-[hsl(var(--foreground))] opacity-80">
                  Fetching your project&apos;s task history
                </p>
              </div>
            </div>
          ) : /* Error state */
          tasksError ? (
            <div className="flex flex-1 items-center justify-center py-20">
              <div className="mx-auto max-w-md px-4 text-center">
                <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-red-100">
                  <AlertTriangle className="h-8 w-8 text-red-400" />
                </div>
                <h3 className="mb-2 text-lg font-medium text-[hsl(var(--foreground))]">
                  Failed to load tasks
                </h3>
                <p className="mb-4 text-[hsl(var(--foreground))] opacity-80">
                  {tasksError.message || 'Unable to fetch task history'}
                </p>
                <Button
                  onClick={() => window.location.reload()}
                  variant="outline"
                  className="cursor-pointer"
                >
                  Try Again
                </Button>
              </div>
            </div>
          ) : visibleChats.length > 0 ? (
            <div className="px-4 py-6 sm:px-8">
              <div className="divide-y divide-[hsl(var(--border))]">
                {visibleChats.map((chat) => (
                  <div
                    key={chat.id}
                    className="group -mx-4 flex cursor-pointer items-start gap-4 px-4 py-5 transition-all duration-200 hover:bg-gray-50"
                    onClick={() => handleChatClick(chat.id)}
                    role="button"
                    tabIndex={0}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        handleChatClick(chat.id);
                      }
                    }}
                    aria-label={`Open chat: ${chat.title}`}
                  >
                    {/* Icon */}
                    <div className="flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-lg bg-[hsl(var(--muted))] transition-all duration-200 group-hover:bg-[hsl(var(--primary))]/8">
                      <div className="text-[hsl(var(--primary))]">
                        {chat.emoji}
                      </div>
                    </div>

                    {/* Content */}
                    <div className="min-w-0 flex-1">
                      {/* Title and Menu */}
                      <div className="mb-1 flex items-start justify-between">
                        <h3 className="truncate pr-2 text-base leading-[164.99%] font-medium text-[hsl(var(--foreground))] transition-colors group-hover:text-[hsl(var(--primary))]">
                          {chat.title}
                        </h3>

                        {/* Menu Button */}
                        <div
                          className="relative"
                          ref={openMenuId === chat.id ? menuRef : null}
                        >
                          <button
                            onClick={(e) => handleMenuClick(chat.id, e)}
                            className="flex h-[32px] w-[32px] flex-shrink-0 cursor-pointer items-center justify-center rounded-lg border border-gray-100/50 bg-white/90 opacity-0 backdrop-blur-sm transition-all duration-200 group-hover:opacity-100 hover:bg-white hover:shadow-sm"
                            aria-label={`More options for ${chat.title}`}
                          >
                            <MoreVertical className="h-4 w-4 text-[hsl(var(--muted-foreground))]" />
                          </button>

                          {/* Dropdown Menu */}
                          {openMenuId === chat.id && (
                            <div className="animate-in fade-in-0 zoom-in-95 absolute top-10 right-0 z-50 w-44 rounded-xl border border-gray-200 bg-white py-2 shadow-lg duration-100">
                              <button
                                onClick={(e) => handleRename(chat.id, e)}
                                className="flex w-full cursor-pointer items-center gap-3 px-3 py-2.5 text-sm text-[hsl(var(--foreground))] transition-colors hover:bg-gray-50"
                              >
                                <Edit3 className="h-4 w-4" />
                                Rename
                              </button>
                              <button
                                onClick={(e) => handleDelete(chat.id, e)}
                                className="flex w-full cursor-pointer items-center gap-3 px-3 py-2.5 text-sm text-red-600 transition-colors hover:bg-red-50"
                              >
                                <Trash2 className="h-4 w-4" />
                                Delete
                              </button>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Timestamp */}
                      <div className="mb-2">
                        <span className="text-sm leading-[164.99%] text-[hsl(var(--foreground))] opacity-80">
                          {chat.timestamp}
                        </span>
                      </div>

                      {/* Description */}
                      <p className="line-clamp-2 text-sm leading-[164.99%] text-[hsl(var(--foreground))]">
                        {chat.description}
                      </p>
                    </div>
                  </div>
                ))}

                {/* Show More Button */}
                {hasMoreChats && (
                  <div className="flex justify-center pt-8 pb-6">
                    <Button
                      onClick={handleShowMore}
                      disabled={isLoading}
                      variant="outline"
                      className="min-w-[200px] cursor-pointer border-[hsl(var(--border))] text-[hsl(var(--foreground))] hover:border-[hsl(var(--primary))] hover:text-[hsl(var(--primary))] disabled:cursor-not-allowed disabled:opacity-50"
                    >
                      {isLoading ? (
                        <SpinLoader size="sm" showText text="Loading more chats..." />
                      ) : (
                        'Show More'
                      )}
                    </Button>
                  </div>
                )}
              </div>
            </div>
          ) : (
            /* Empty State */
            <div className="flex flex-1 items-center justify-center py-20">
              <div className="mx-auto max-w-md px-4 text-center">
                <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-muted">
                  <MessageSquare className="h-8 w-8 text-muted-foreground" />
                </div>
                <h3 className="mb-2 text-lg font-medium text-[hsl(var(--foreground))]">
                  {searchQuery ? 'No chats found' : 'No chat history yet'}
                </h3>
                <p className="mb-6 text-[hsl(var(--foreground))] opacity-80">
                  {searchQuery
                    ? `Try adjusting your search term "${searchQuery}"`
                    : 'Start a conversation in this project to see your chat history here'}
                </p>
                {!searchQuery && (
                  <Button
                    onClick={handleNewChat}
                    className="bg-[hsl(var(--primary))] text-white hover:bg-[hsl(var(--primary))] hover:opacity-90"
                  >
                    Start New Chat
                  </Button>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
