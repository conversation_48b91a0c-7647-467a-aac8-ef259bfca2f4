'use client';

import React, {
  useState,
  useMemo,
  useCallback,
  useRef,
  useEffect,
} from 'react';
import {
  MoreVertical,
  Edit3,
  Trash2,
  MessageSquare
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { SpinLoader } from '@/components/ui/spin-loader';

// Types
interface ChatItem {
  id: string;
  title: string;
  description: string;
  timestamp: string;
  emoji: React.ReactNode;
  createdAt: Date;
  messageCount: number;
}

interface ChatHistoryProps {
  onChatClick?: (chatId: string) => void;
  onNewChat?: () => void;
  className?: string;
  chats?: ChatItem[];
  isLoading?: boolean;
  error?: Error | null;
}

// Mock data - In production, this would come from an API
const generateMockChats = (): ChatItem[] => {
  const getIcon = () => {
    return <MessageSquare key="chat" className="h-5 w-5" />;
  };
  const titles = [
    'Product Card.tsx',
    'Mobile App Architecture Design',
    'Mobile Banking App',
    'E-commerce Dashboard',
    'React Hook Form Validation',
    'API Integration Guide',
    'Database Schema Planning',
    'UI Component Library',
    'Authentication System',
    'Payment Gateway Setup',
    'Social Media App',
    'Task Management Tool',
    'Weather App Widget',
    'Chat Application',
    'Portfolio Website',
    'Blog CMS System',
    'Inventory Management',
    'Customer Support Bot',
    'Analytics Dashboard',
    'Video Streaming App',
  ];

  const descriptions = [
    'Refining product card react component with advanced animations',
    'Full-stack e-commerce solution with React, Node.js, and MongoDB for a modern online store',
    'React Native application with biometric authentication, real-time transactions, and advanced security features',
    'Building responsive dashboard with TypeScript and Next.js',
    'Implementing form validation with Zod and React Hook Form',
    'Setting up REST API endpoints with proper error handling',
    'Designing scalable database architecture for microservices',
    'Creating reusable UI components with Storybook documentation',
    'JWT-based authentication with refresh token rotation',
    'Integrating Stripe payment processing with webhook handling',
    'Social platform with real-time messaging and content sharing',
    'Kanban-style task management with drag and drop functionality',
    'Weather forecast widget with geolocation and animations',
    'Real-time chat application with WebSocket connections',
    'Personal portfolio with modern animations and dark mode',
    'Content management system with rich text editor',
    'Inventory tracking system with barcode scanning',
    'AI-powered customer support chatbot integration',
    'Business analytics dashboard with interactive charts',
    'Video streaming platform with adaptive bitrate streaming',
  ];

  return Array.from({ length: 50 }, (_, index) => ({
    id: (index + 1).toString(), // Clean numeric IDs: "1", "2", "3", etc.
    title: titles[index % titles.length],
    description: descriptions[index % descriptions.length],
    timestamp:
      index < 5
        ? 'Yesterday'
        : `Updated ${Math.floor(Math.random() * 30) + 1} days ago`,
    emoji: getIcon(),
    createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
    messageCount: Math.floor(Math.random() * 50) + 1,
  }));
};

export function ChatHistory({
  onChatClick,
  onNewChat,
  className = '',
  chats,
  isLoading: externalLoading = false,
  error,
}: ChatHistoryProps) {
  // State management
  const [searchQuery, setSearchQuery] = useState('');
  const [visibleCount, setVisibleCount] = useState(6);
  const [isLoading, setIsLoading] = useState(false);
  const [openMenuId, setOpenMenuId] = useState<string | null>(null);
  const menuRef = useRef<HTMLDivElement>(null);

  // Use external chats if provided, otherwise use mock data
  const allChats = chats || generateMockChats();

  // Memoized filtered chats based on search query
  const filteredChats = useMemo(() => {
    if (!searchQuery.trim()) return allChats;

    const query = searchQuery.toLowerCase();
    return allChats.filter(
      (chat) =>
        chat.title.toLowerCase().includes(query) ||
        chat.description.toLowerCase().includes(query)
    );
  }, [allChats, searchQuery]);

  // Visible chats based on pagination
  const visibleChats = useMemo(
    () => filteredChats.slice(0, visibleCount),
    [filteredChats, visibleCount]
  );

  const hasMoreChats = filteredChats.length > visibleCount;

  // Close menu when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setOpenMenuId(null);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [setOpenMenuId]);

  // Event handlers
  const handleChatClick = useCallback(
    (chatId: string) => {
      onChatClick?.(chatId);
    },
    [onChatClick]
  );

  const handleNewChat = useCallback(() => {
    onNewChat?.();
  }, [onNewChat]);

  const handleSearchChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setSearchQuery(e.target.value);
      setVisibleCount(6); // Reset pagination when searching
    },
    []
  );

  const handleShowMore = useCallback(async () => {
    setIsLoading(true);

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 800));

    setVisibleCount((prev) => prev + 6);
    setIsLoading(false);
  }, [setVisibleCount, setIsLoading]);

  const handleMenuClick = useCallback(
    (chatId: string, e: React.MouseEvent) => {
      e.stopPropagation();
      setOpenMenuId(openMenuId === chatId ? null : chatId);
    },
    [openMenuId]
  );

  const handleRename = useCallback((chatId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setOpenMenuId(null);
    // Handle rename logic here
    console.log('Rename chat:', chatId);
    // In production, you might show a rename modal or inline edit
  }, [setOpenMenuId]);

  const handleDelete = useCallback((chatId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setOpenMenuId(null);
    // Handle delete logic here
    console.log('Delete chat:', chatId);
    // In production, you might show a confirmation dialog
  }, [setOpenMenuId]);

  return (
    <div className={`relative flex h-full flex-col bg-background ${className}`}>
      {/* Decorative background element */}
      <div className="pointer-events-none absolute top-0 right-0 h-[302px] w-[446px] overflow-hidden rounded-full bg-[hsl(var(--primary))] opacity-40 blur-[240px]" />

      {/* Header - Fixed */}
      <div className="relative flex-shrink-0 border-b border-border">
        <div className="mx-auto flex max-w-[1380px] items-center justify-between px-4 py-6 sm:px-8">
          <h1 className="text-xl leading-[150%] font-semibold text-[hsl(var(--foreground))] sm:text-2xl">
            Your chat history
          </h1>

          <div className="flex items-center gap-2 sm:gap-4">
            {/* Search Input */}
            <div className="relative">
              <Input
                value={searchQuery}
                onChange={handleSearchChange}
                placeholder="Search your chats..."
                className="h-[38px] w-48 rounded-lg border-[hsl(var(--border))] bg-[hsl(var(--muted))] px-4 text-sm transition-colors placeholder:text-[hsl(var(--foreground))] placeholder:opacity-70 focus:border-[hsl(var(--primary))] focus:ring-[hsl(var(--primary))] sm:w-64"
                aria-label="Search chat history"
              />
            </div>

            {/* New Chat Button */}
            <Button
              onClick={handleNewChat}
              className="h-[38px] rounded-md bg-[hsl(var(--primary))] px-3 text-sm font-medium text-white transition-colors hover:bg-[hsl(var(--primary))] hover:opacity-90 sm:px-4"
              aria-label="Start new chat"
            >
              <span className="hidden sm:inline">New Chat</span>
              <span className="sm:hidden">New</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Search Results Info - Fixed */}
      {searchQuery && (
        <div className="relative flex-shrink-0 border-b border-gray-50">
          <div className="mx-auto max-w-[1380px] px-4 py-3 sm:px-8">
            <p className="text-sm text-[hsl(var(--foreground))] opacity-70">
              {filteredChats.length === 0
                ? `No chats found for "${searchQuery}"`
                : `${filteredChats.length} chat${filteredChats.length === 1 ? '' : 's'} found`}
            </p>
          </div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="relative flex-shrink-0 border-b border-red-100 bg-red-50">
          <div className="mx-auto max-w-[1380px] px-4 py-3 sm:px-8">
            <p className="text-sm text-red-600">
              Error loading chats: {error.message}
            </p>
          </div>
        </div>
      )}

      {/* Loading State */}
      {externalLoading ? (
        <div className="flex flex-1 items-center justify-center py-20">
          <SpinLoader size="lg" showText text="Loading chats..." />
        </div>
      ) : (
        /* Chat List - Scrollable */
        <div className="flex-1 overflow-y-auto">
          <div className="mx-auto max-w-[1380px]">
            {visibleChats.length > 0 ? (
            <div className="px-4 py-6 sm:px-8">
              <div className="divide-y divide-[hsl(var(--border))]">
                {visibleChats.map((chat) => (
                  <div
                    key={chat.id}
                    className="group -mx-4 flex cursor-pointer items-start gap-4 px-4 py-5 transition-all duration-200 hover:bg-gray-50"
                    onClick={() => handleChatClick(chat.id)}
                    role="button"
                    tabIndex={0}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        handleChatClick(chat.id);
                      }
                    }}
                    aria-label={`Open chat: ${chat.title}`}
                  >
                    {/* Icon */}
                    <div className="flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-lg bg-[hsl(var(--muted))] transition-all duration-200 group-hover:bg-[hsl(var(--primary))]/8">
                      <div className="text-[hsl(var(--primary))]">
                        {chat.emoji}
                      </div>
                    </div>

                    {/* Content */}
                    <div className="min-w-0 flex-1">
                      {/* Title and Menu */}
                      <div className="mb-1 flex items-start justify-between">
                        <h3 className="truncate pr-2 text-base leading-[164.99%] font-medium text-[hsl(var(--foreground))] transition-colors group-hover:text-[hsl(var(--primary))]">
                          {chat.title}
                        </h3>

                        {/* Menu Button */}
                        <div
                          className="relative"
                          ref={openMenuId === chat.id ? menuRef : null}
                        >
                          <button
                            onClick={(e) => handleMenuClick(chat.id, e)}
                            className="flex h-[32px] w-[32px] flex-shrink-0 cursor-pointer items-center justify-center rounded-lg border border-gray-100/50 bg-white/90 opacity-0 backdrop-blur-sm transition-all duration-200 group-hover:opacity-100 hover:bg-white hover:shadow-sm"
                            aria-label={`More options for ${chat.title}`}
                          >
                            <MoreVertical className="h-4 w-4 text-[hsl(var(--muted-foreground))]" />
                          </button>

                          {/* Dropdown Menu */}
                          {openMenuId === chat.id && (
                            <div className="animate-in fade-in-0 zoom-in-95 absolute top-10 right-0 z-50 w-44 rounded-xl border border-gray-200 bg-white py-2 shadow-lg duration-100">
                              <button
                                onClick={(e) => handleRename(chat.id, e)}
                                className="flex w-full cursor-pointer items-center gap-3 px-3 py-2.5 text-sm text-[hsl(var(--foreground))] transition-colors hover:bg-gray-50"
                              >
                                <Edit3 className="h-4 w-4" />
                                Rename
                              </button>
                              <button
                                onClick={(e) => handleDelete(chat.id, e)}
                                className="flex w-full cursor-pointer items-center gap-3 px-3 py-2.5 text-sm text-red-600 transition-colors hover:bg-red-50"
                              >
                                <Trash2 className="h-4 w-4" />
                                Delete
                              </button>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Timestamp */}
                      <div className="mb-2">
                        <span className="text-sm leading-[164.99%] text-[hsl(var(--foreground))] opacity-80">
                          {chat.timestamp}
                        </span>
                      </div>

                      {/* Description */}
                      <p className="line-clamp-2 text-sm leading-[164.99%] text-[hsl(var(--foreground))]">
                        {chat.description}
                      </p>
                    </div>
                  </div>
                ))}

                {/* Show More Button */}
                {hasMoreChats && (
                  <div className="flex justify-center pt-8 pb-6">
                    <Button
                      onClick={handleShowMore}
                      disabled={isLoading}
                      variant="outline"
                      className="min-w-[200px] cursor-pointer border-[hsl(var(--border))] text-[hsl(var(--foreground))] hover:border-[hsl(var(--primary))] hover:text-[hsl(var(--primary))] disabled:cursor-not-allowed disabled:opacity-50"
                    >
                      {isLoading ? (
                        <SpinLoader size="sm" showText text="Loading more chats..." />
                      ) : (
                        'Show More'
                      )}
                    </Button>
                  </div>
                  )}
                </div>
              </div>
            ) : (
              /* Empty State */
              <div className="flex flex-1 items-center justify-center py-20">
                <div className="mx-auto max-w-md px-4 text-center">
                  <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-muted">
                    <MessageSquare className="h-8 w-8 text-muted-foreground" />
                  </div>
                  <h3 className="mb-2 text-lg font-medium text-[hsl(var(--foreground))]">
                    {searchQuery ? 'No chats found' : 'No chat history yet'}
                  </h3>
                  <p className="mb-6 text-[hsl(var(--foreground))] opacity-80">
                    {searchQuery
                      ? `Try adjusting your search term "${searchQuery}"`
                      : 'Start a conversation to see your chat history here'}
                  </p>
                  {!searchQuery && (
                    <Button
                      onClick={handleNewChat}
                      className="bg-[hsl(var(--primary))] text-white hover:bg-[hsl(var(--primary))] hover:opacity-90"
                    >
                      Start New Chat
                    </Button>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
