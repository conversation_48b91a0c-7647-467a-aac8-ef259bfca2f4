'use client';

import React, { useCallback } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useSidebar } from '@/components/sidebar/sidebar-context';
import { NewChat } from './new-chat';
import { ChatHistory } from './chat-history';
import { Projects } from './projects';
import ChatView from './chat-view';

export function MainContent() {
  const { state } = useSidebar();
  const router = useRouter();
  const params = useParams<{ tenant: string }>();
  const tenant = (params?.tenant as string) || '';

  // Handle project click navigation
  const handleProjectClick = useCallback(
    (projectId: string) => {
      if (!tenant) return;
      router.push(`/${tenant}/${projectId}/chat`);
    },
    [router, tenant]
  );

  // Handle new project creation
  const handleNewProject = useCallback(() => {
    // Handle new project creation
    console.log('Create new project');
    // In production, you might show a modal or navigate to a creation page
  }, []);

  // Handle chat click navigation from main content
  const handleChatClick = useCallback(
    (chatId: string) => {
      if (!tenant) return;
      router.push(`/${tenant}/chats/${chatId}`);
    },
    [router, tenant]
  );

  // Handle new chat navigation from main content
  const handleNewChat = useCallback(() => {
    if (!tenant) return;
    router.push(`/${tenant}/newchat`);
  }, [router, tenant]);

  const renderContent = () => {
    switch (state.activeItem) {
      case 'new-chat':
        return <NewChat />;
      case 'chat-history':
        return (
          <ChatHistory
            onChatClick={handleChatClick}
            onNewChat={handleNewChat}
          />
        );
      case 'projects':
        return (
          <Projects
            onProjectClick={handleProjectClick}
            onNewProject={handleNewProject}
          />
        );
      case 'chat':
        return <ChatView />;
      default:
        return <NewChat />; // Default to new chat
    }
  };

  return <div className="h-full flex-1 overflow-hidden">{renderContent()}</div>;
}
