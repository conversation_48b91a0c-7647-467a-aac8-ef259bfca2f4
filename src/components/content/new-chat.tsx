'use client';

import React, { useCallback } from 'react';
import Image from 'next/image';
import ChatInput from './chat-input';
import { useChatStore } from '@/store/chat';
import { useComponentStore } from '@/store/components';
import { useSidebar } from '@/components/sidebar/sidebar-context';
import { useTheme } from '@/components/theme/theme-provider';

export function NewChat() {
  const {
    newChatInput: inputValue,
    newChatAttachmentCount: attachmentCount,
    setNewChatInput: setInputValue,
    setNewChatAttachmentCount: setAttachmentCount
  } = useComponentStore();

  // Chat state + navigation to chat interface
  const startNewChat = useChatStore((s) => s.startNewChat);
  const simulateAssistantResponse = useChatStore(
    (s) => s.simulateAssistantResponse
  );
  const { setActiveItem } = useSidebar();
  const { actualTheme } = useTheme();


  const recommendations = [
    {
      emoji: '🚀',
      title: 'Start New Project',
      description: 'Create something amazing',
      category: 'project',
    },
    {
      emoji: '💡',
      title: 'Code Review',
      description: 'Improve your code',
      category: 'code',
    },
    {
      emoji: '🎨',
      title: 'Design Help',
      description: 'Build beautiful interfaces',
      category: 'design',
    },
    {
      emoji: '🔧',
      title: 'Debug Issues',
      description: 'Fix bugs quickly',
      category: 'debug',
    },
  ];

  const baseTop = attachmentCount > 0 ? 527 : 445;
  const recommendationsToRender = recommendations.slice(0, 4);

  // Handle recommendation click
  const handleRecommendationClick = (category: string, title: string) => {
    const prompts = {
      project:
        'I want to start a new project. Can you help me set up the structure and choose the right technologies?',
      code: 'I need help reviewing and optimizing my code. Can you help me identify improvements and best practices?',
      design:
        'I want to create a modern UI/UX design. Can you help me with design patterns and component libraries?',
      data: 'I have data that I need to analyze. Can you help me extract insights and create visualizations?',
      debug:
        "I'm facing some bugs and performance issues in my code. Can you help me troubleshoot and fix them?",
      learn:
        'I want to learn a new technology or framework. Can you provide me with a structured learning path?',
    };

    const prompt =
      prompts[category as keyof typeof prompts] ||
      `Help me with ${title.toLowerCase()}`;
    startNewChat(prompt);
    setActiveItem('chat');
    setInputValue('');
    simulateAssistantResponse(
      `I'd be happy to help you with ${title.toLowerCase()}! Let's get started.`,
      900
    );
  };

  // Memoize the onFilesChange callback to prevent infinite re-renders
  const handleFilesChange = useCallback((count: number) => {
    setAttachmentCount(count);
  }, [setAttachmentCount]);

  return (
    <div className="overflow-hidden relative h-full bg-background">
      {/* Background blur ellipse - exact position from Figma */}
      <div
        className="absolute"
        style={{
          left: '1003px',
          top: '-240px',
          width: '446px',
          height: '302px',
          background: 'hsl(var(--primary) / 0.4)',
          borderRadius: '50%',
          filter: 'blur(240px)',
          transform: 'translateX(-50%)',
        }}
      />

      {/* Logo - exact position from Figma */}
      <div
        className="flex absolute justify-center items-center"
        style={{
          left: '50%',
          top: '174px',
          transform: 'translateX(-50%)',
          width: '119px',
          height: '36px',
        }}
      >
        <div className="flex items-center">
          <Image 
            src={actualTheme === 'dark' ? "/kavia-logo-white.svg" : "/logo.svg"} 
            alt="Kavia logo" 
            width={137.034} 
            height={36} 
          />
        </div>
      </div>

      <ChatInput
        value={inputValue}
        onChange={setInputValue}
        onSend={(val) => {
          startNewChat(val);
          setActiveItem('chat');
          setInputValue('');
          // Demo: show immediate thinking and a friendly first reply
          simulateAssistantResponse(
            "Hey there! How's it going? What can I help you with today?",
            900
          );
        }}
        onFilesChange={handleFilesChange}
      />

      {/* Recommendations section title */}
      <p
        className="absolute text-center"
        style={{
          left: '50%',
          top: attachmentCount > 0 ? '488px' : '406px',
          transition: 'top 250ms ease',
          transform: 'translateX(-50%)',
          width: '592px',
          color: 'hsl(var(--foreground) / 0.7)',
          fontSize: '14px',
          fontFamily: 'Inter, sans-serif',
          fontWeight: '400',
          lineHeight: '23px',
          margin: 0,
        }}
      >
        What would you like to work on today?
      </p>

      {/* Mobile responsive CSS for recommendations */}
      <style jsx>{`
        @media screen and (max-width: 768px) {
          p[style*='width: 592px'] {
            left: 16px !important;
            right: 16px !important;
            width: auto !important;
            transform: none !important;
            top: ${attachmentCount > 0 ? '390px' : '330px'} !important;
            font-size: 14px !important;
          }

          .recommendation-card {
            left: 16px !important;
            right: 16px !important;
            width: auto !important;
            transform: none !important;
            height: 56px !important;
            padding: 8px 12px !important;
            border-radius: 8px !important;
            background: hsl(var(--muted) / 0.8) !important;
            margin-bottom: 8px !important;
          }

          .recommendation-card:nth-child(5) {
            top: ${attachmentCount > 0 ? '440px' : '380px'} !important;
          }

          .recommendation-card:nth-child(6) {
            top: ${attachmentCount > 0 ? '508px' : '448px'} !important;
          }

          .recommendation-card:nth-child(7) {
            top: ${attachmentCount > 0 ? '576px' : '516px'} !important;
          }

          .recommendation-card:nth-child(8) {
            top: ${attachmentCount > 0 ? '644px' : '584px'} !important;
          }

          /* Make emoji containers smaller on mobile */
          .recommendation-card > div:first-child {
            width: 40px !important;
            height: 40px !important;
            font-size: 16px !important;
          }

          /* Adjust text sizes on mobile */
          .recommendation-card span[style*='font-size: 14px'] {
            font-size: 13px !important;
          }

          .recommendation-card span[style*='font-size: 12px'] {
            font-size: 11px !important;
          }
        }
      `}</style>

      {/* Simple recommendation cards */}
      {recommendationsToRender.map((recommendation, index) => (
        <div
          key={index}
          className="absolute transition-opacity cursor-pointer recommendation-card hover:opacity-80"
          onClick={() =>
            handleRecommendationClick(
              recommendation.category,
              recommendation.title
            )
          }
          style={{
            left: '50%',
            top: `${baseTop + index * 64}px`,
            transition: 'top 250ms ease',
            transform: 'translateX(-50%)',
            width: '616px',
            height: '48px',
            display: 'flex',
            alignItems: 'center',
            gap: '16px',
          }}
        >
          {/* Emoji container */}
          <div
            style={{
              width: '48px',
              height: '48px',
              background: 'hsl(var(--muted))',
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '18px',
              fontFamily: 'Inter, sans-serif',
              lineHeight: '27px',
            }}
          >
            {recommendation.emoji}
          </div>

          {/* Content */}
          <div
            style={{
              flex: 1,
              height: '43px',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
            }}
          >
            {/* Title */}
            <div
              style={{ height: '23px', display: 'flex', alignItems: 'center' }}
            >
              <span
                style={{
                  color: 'hsl(var(--foreground))',
                  fontSize: '14px',
                  fontFamily: 'Inter, sans-serif',
                  fontWeight: '500',
                  lineHeight: '23px',
                }}
              >
                {recommendation.title}
              </span>
            </div>

            {/* Description */}
            <div
              style={{ height: '20px', display: 'flex', alignItems: 'center' }}
            >
              <span
                style={{
                  color: 'hsl(var(--foreground) / 0.8)',
                  fontSize: '12px',
                  fontFamily: 'Inter, sans-serif',
                  fontWeight: '400',
                  lineHeight: '20px',
                }}
              >
                {recommendation.description}
              </span>
            </div>
          </div>
        </div>
      ))}

      {/* Bottom gradient overlay - exact specs from Figma */}
      {/* <div
        className="fixed bottom-0"
        style={{
          left: '60px',
          width: 'calc(100vw - 120px)',
          maxWidth: '1380px',
          height: '131px',
          background: 'linear-gradient(0deg, hsl(var(--background)) 0%, hsl(var(--background) / 0.45) 100%)',
          pointerEvents: 'none',
          marginLeft: '50%',
          transform: 'translateX(-50%)',
        }}
      /> */}

      {/* ChatInput includes hidden textarea and focus overlay */}

      {/* Mobile responsive adjustments */}
      <style jsx>{`
        @media (max-width: 768px) {
          .absolute[style*='width: 616px'] {
            width: calc(100vw - 40px) !important;
            left: 20px !important;
            transform: none !important;
          }

          .absolute[style*='width: 592px'] {
            width: calc(100vw - 60px) !important;
            left: 30px !important;
            transform: none !important;
          }
        }

        @media (max-width: 480px) {
          .absolute[style*='top: 174px'] {
            top: 100px !important;
          }

          .absolute[style*='top: 406px'] {
            top: 320px !important;
          }

          .absolute[style*='top: 445px'] {
            top: 360px !important;
          }
        }
      `}</style>
    </div>
  );
}
