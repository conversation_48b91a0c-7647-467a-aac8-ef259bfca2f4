interface LoginHeaderProps {
  title?: string;
  subtitle?: string;
}

export function LoginHeader({ 
  title = "Sign in to Kavia",
  subtitle = "Welcome back! Please sign in to continue"
}: LoginHeaderProps) {
  return (
    <div className="text-center space-y-2">
      <h1 className="text-xl font-semibold tracking-tight text-gray-900">
        {title}
      </h1>
      <p className="text-gray-600">
        {subtitle}
      </p>
    </div>
  );
}