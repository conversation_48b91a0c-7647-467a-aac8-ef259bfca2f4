interface SignUpPromptProps {
  onSignUpClick: () => void;
  className?: string;
}

export function SignUpPrompt({ 
  onSignUpClick,
  className = ""
}: SignUpPromptProps) {
  return (
    <div className={`text-center ${className}`}>
      <p className="text-sm text-gray-600">
        Don&apos;t have an account?{' '}
        <button
          type="button"
          className="text-orange-500 hover:text-orange-600 font-medium cursor-pointer"
          onClick={onSignUpClick}
        >
          Sign up
        </button>
      </p>
    </div>
  );
}