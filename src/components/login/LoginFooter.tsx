interface LoginFooterProps {
  onSupportClick?: () => void;
  onPrivacyClick?: () => void;
  onTermsClick?: () => void;
  className?: string;
}

export function LoginFooter({ 
  onSupportClick = () => console.log('Support clicked'),
  onPrivacyClick = () => console.log('Privacy clicked'),
  onTermsClick = () => console.log('Terms clicked'),
  className = ""
}: LoginFooterProps) {
  return (
    <div className={`text-center text-sm text-muted-foreground ${className}`}>
      <div className="flex items-center justify-center space-x-2">
        <span>© 2024 KAVIA AI</span>
        <span>|</span>
        <button
          type="button"
          className="hover:text-foreground transition-colors"
          onClick={onSupportClick}
        >
          Support
        </button>
        <span>|</span>
        <button
          type="button"
          className="hover:text-foreground transition-colors"
          onClick={onPrivacyClick}
        >
          Privacy
        </button>
        <span>|</span>
        <button
          type="button"
          className="hover:text-foreground transition-colors"
          onClick={onTermsClick}
        >
          Terms
        </button>
      </div>
    </div>
  );
}