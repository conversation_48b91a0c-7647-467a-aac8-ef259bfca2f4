interface DividerProps {
  text?: string;
  className?: string;
}

export function Divider({
  text = "Or",
  className = ""
}: DividerProps) {
  return (
    <div className={`relative ${className}`}>
      <div className="absolute inset-0 flex items-center">
        <span className="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent" />
      </div>
      <div className="relative flex justify-center text-xs uppercase">
        <span className="bg-background px-2 text-muted-foreground">
          {text}
        </span>
      </div>
    </div>
  );
}