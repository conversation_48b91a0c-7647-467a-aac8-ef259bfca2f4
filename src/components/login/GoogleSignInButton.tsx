import { But<PERSON> } from '@/components/ui/button';
import { GoogleIcon } from '@/components/icons/GoogleIcon';
import { useGoogleAuth } from '@/hooks/useAuth';

interface GoogleSignInButtonProps {
  disabled?: boolean;
  className?: string;
}

export function GoogleSignInButton({
  disabled = false,
  className = ""
}: GoogleSignInButtonProps) {
  const googleAuth = useGoogleAuth();

  const handleGoogleSignIn = () => {
    googleAuth.mutate();
  };

  return (
    <Button
      variant="outline"
      onClick={handleGoogleSignIn}
      disabled={disabled || googleAuth.isPending}
      className={`w-full h-11 text-sm font-medium text-foreground border-border cursor-pointer ${className}`}
    >
      <div className="flex items-center justify-center gap-3">
        <GoogleIcon className="w-5 h-5" />
        {googleAuth.isPending ? 'Redirecting...' : 'Continue with Google'}
      </div>
    </Button>
  );
}