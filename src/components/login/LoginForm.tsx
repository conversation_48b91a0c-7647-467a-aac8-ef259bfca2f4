'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ArrowIcon } from '@/components/icons/ArrowIcon';
import { LoginHeader } from './LoginHeader';
import { GoogleSignInButton } from './GoogleSignInButton';
import { Divider } from './Divider';
import { LoginFooter } from './LoginFooter';
import { useUserOrganizations, useLogin } from '@/hooks/useAuth';
import { encryptString } from '@/utils/hash';
import { useFormsStore } from '@/store/forms';
import Cookies from 'js-cookie';
import { decodeJwt } from 'jose';
import config from '@/lib/config';

export function LoginForm() {
  const router = useRouter();
  const {
    email,
    password,
    selectedOrganization,
    shouldFetchOrganizations,
    isLoading,
    errors,
    setEmail,
    setPassword,
    setSelectedOrganization,
    setShouldFetchOrganizations,
    setIsLoading,
    setError,
    clearError,
    resetForm
  } = useFormsStore();

  // Fetch organizations when email is provided and shouldFetchOrganizations is true
  const { data: organizationsData, isLoading: isLoadingOrganizations, error: organizationsError } = useUserOrganizations(
    email,
    shouldFetchOrganizations && !!email
  );

  // Use login mutation hook
  const loginMutation = useLogin();

  // LocalStorage helper functions
  const getTenantsByEmail = () => {
    try {
      const stored = localStorage.getItem('tenantsByEmail');
      return stored ? JSON.parse(stored) : {};
    } catch (error) {
      console.error('Error parsing tenantsByEmail from localStorage:', error);
      return {};
    }
  };

  const updateTenantsByEmail = (email: string, tenantId: string, tenantName: string) => {
    try {
      const tenantsByEmail = getTenantsByEmail();
      tenantsByEmail[email] = {
        pre_tenant_id: tenantId,
        pre_tenant_name: tenantName
      };
      localStorage.setItem('tenantsByEmail', JSON.stringify(tenantsByEmail));
    } catch (error) {
      console.error('Error updating tenantsByEmail in localStorage:', error);
    }
  };

  // Email validation function to check for complete valid emails
  const isValidCompleteEmail = (email: string): boolean => {
    // Basic email regex that ensures:
    // - Has characters before @
    // - Has @ symbol
    // - Has domain name after @
    // - Has at least one dot in domain
    // - Domain extension is at least 2 characters
    const emailRegex = /^[^\s@]+@[^\s@]+\.[a-zA-Z]{2,}$/;
    return emailRegex.test(email.trim());
  };

  // Auto-select organization based on priority when organizations are loaded
  useEffect(() => {
    if (organizationsData && organizationsData.organizations.length > 0 && !selectedOrganization) {
      const organizations = organizationsData.organizations;
      const tenantsByEmail = getTenantsByEmail();
      
      let selectedOrgId = '';
      
      // Priority 1: Check if email exists in localStorage tenantsByEmail
      if (email && tenantsByEmail[email] && tenantsByEmail[email].pre_tenant_id) {
        const savedTenantId = tenantsByEmail[email].pre_tenant_id;
        const foundOrg = organizations.find(org => org.id === savedTenantId);
        if (foundOrg) {
          selectedOrgId = savedTenantId;
        }
      }
      
      // Priority 2: If not in localStorage, check for B2C tenant ID from config
      if (!selectedOrgId && config.B2C_TENANT_ID) {
        const b2cOrg = organizations.find(org => org.id === config.B2C_TENANT_ID);
        if (b2cOrg) {
          selectedOrgId = config.B2C_TENANT_ID;
        }
      }
      
      // Priority 3: Default to first organization
      if (!selectedOrgId && organizations.length > 0) {
        selectedOrgId = organizations[0].id;
      }
      
      if (selectedOrgId) {
        setSelectedOrganization(selectedOrgId);
      }
    }
  }, [organizationsData, selectedOrganization, email, setSelectedOrganization]);

  // Handle organization selection change
  const handleOrganizationChange = (orgId: string) => {
    setSelectedOrganization(orgId);
    
    // Update localStorage when organization is selected
    if (email && organizationsData) {
      const selectedOrg = organizationsData.organizations.find(org => org.id === orgId);
      if (selectedOrg) {
        updateTenantsByEmail(email, orgId, selectedOrg.name);
      }
    }
  };


  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Prepare login data according to LoginRequest interface
    const loginData: { email: string; password: string; organization_id?: string } = {
      email,
      password
    };
    
    // Include organization_id if selected
    if (selectedOrganization) {
      loginData.organization_id = encryptString(selectedOrganization);
    }
    
    // Set loading state
    setIsLoading(true);
    clearError('general');
    
    // Call the login mutation
    loginMutation.mutate(loginData, {
      onSuccess: (data) => {
        console.log('Login successful:', data);
        setIsLoading(false);
        resetForm();
        
        // Store cookies with appropriate expiration times
        // id_token expires in 24 hours
        Cookies.set('idToken', data.id_token, { expires: 1 }); // 1 day
        
        // Other tokens and data expire in 30 days
        Cookies.set('refresh_token', data.refresh_token, { expires: 30 });
        Cookies.set('selected_tenant_id', data.tenant_id, { expires: 30 });
        Cookies.set('is_admin', data.is_admin.toString(), { expires: 30 });
        Cookies.set('is_free_user', data.is_free_user.toString(), { expires: 30 });
        Cookies.set('is_super_admin', data.is_super_admin.toString(), { expires: 30 });
        Cookies.set('AccessToken', data.AccessToken, { expires: 30 });
        Cookies.set('ExpiresIn', data.ExpiresIn.toString(), { expires: 30 });
        Cookies.set('TokenType', data.TokenType, { expires: 30 });
        
        // Extract tenant_id from JWT token and redirect to tenant-based chat page
        try {
          const decoded = decodeJwt(data.id_token) as { 'custom:tenant_id'?: string; [key: string]: unknown };
          const tenantId = decoded['custom:tenant_id'];
          if (tenantId) {
            router.push(`/${tenantId}/newchat`);
          } else {
            // Fallback to projects page if no tenant_id found
            router.push('/projects');
          }
        } catch (error) {
          console.error('Error decoding JWT for redirect:', error);
          // Fallback redirect
          router.push('/projects');
        }
      },
      onError: (error) => {
        console.error('Login failed:', error.message);
        setIsLoading(false);
        setError('general', error.message);
      }
    });
  };

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newEmail = e.target.value;
    setEmail(newEmail);
    clearError('email');
    
    // Reset organization selection when email changes
    setSelectedOrganization('');
    
    // Check if the email is complete and valid
    if (isValidCompleteEmail(newEmail)) {
      // Trigger organization fetching immediately for valid complete emails
      setShouldFetchOrganizations(true);
    } else {
      // Stop fetching for incomplete or invalid emails
      setShouldFetchOrganizations(false);
    }
  };

  const handleEmailBlur = () => {
    // Optional: Keep blur handler for additional validation if needed
    // Currently, validation happens in real-time during typing
    if (isValidCompleteEmail(email)) {
      setShouldFetchOrganizations(true);
    }
  };

  // const handleSignUpClick = () => {
  //   console.log('Sign up clicked');
  // };

  const handleForgotPasswordClick = () => {
    console.log('Forgot password clicked');
  };

  return (
    <div className="space-y-5 max-w-md mx-auto">
      <Card className="p-7 bg-white">
        <div className="space-y-5">
          <LoginHeader />
          
          <GoogleSignInButton />
          
          <Divider />

          {/* Login Form */}
          <form onSubmit={handleLogin} className="space-y-4">
            {/* Email Field */}
            <div className="space-y-2">
              <Label htmlFor="email" className="text-foreground">Email Address</Label>
              <Input
                id="email"
                type="email"
                placeholder="Email Address"
                value={email}
                onChange={handleEmailChange}
                onBlur={handleEmailBlur}
                required
                className={`h-11 text-foreground placeholder:text-muted-foreground border-0 shadow-[inset_0_0_0_1px_hsl(var(--border))] focus-visible:!shadow-[inset_0_0_0_1px_hsl(var(--primary)),0_0_8px_hsl(var(--primary)/0.4)] focus:!shadow-[inset_0_0_0_1px_hsl(var(--primary)),0_0_8px_hsl(var(--primary)/0.4)] focus-visible:!ring-0 focus-visible:!outline-none focus:!ring-0 focus:!outline-none focus-visible:!ring-ring/0 ${errors.email ? 'shadow-[inset_0_0_0_1px_hsl(var(--destructive))]' : ''}`}
              />
              {errors.email && (
                <p className="text-sm text-destructive">{errors.email}</p>
              )}
            </div>

            {/* Organization Selector - Conditionally rendered */}
            {shouldFetchOrganizations && organizationsData && organizationsData.organizations.length > 0 && (
              <div className="space-y-2">
                <Label htmlFor="organization" className="text-foreground">Select Organization</Label>
                <Select value={selectedOrganization} onValueChange={handleOrganizationChange}>
                  <SelectTrigger className="h-11 text-foreground placeholder:text-muted-foreground border-0 shadow-[inset_0_0_0_1px_hsl(var(--border))] focus-visible:!shadow-[inset_0_0_0_1px_hsl(var(--primary)),0_0_8px_hsl(var(--primary)/0.4)] focus:!shadow-[inset_0_0_0_1px_hsl(var(--primary)),0_0_8px_hsl(var(--primary)/0.4)] focus-visible:!ring-0 focus-visible:!outline-none focus:!ring-0 focus:!outline-none">
                    <SelectValue placeholder="Select an organization" className="text-foreground" />
                  </SelectTrigger>
                  <SelectContent className="bg-background border border-border rounded-md shadow-lg">
                    {organizationsData.organizations.map((org) => (
                      <SelectItem
                        key={org.id}
                        value={org.id}
                        className="relative text-foreground hover:bg-primary/10 data-[state=checked]:bg-primary/10 data-[state=checked]:text-primary pl-3 pr-3 [&>span:first-child]:hidden"
                      >
                        {org.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* Loading state for organizations */}
            {shouldFetchOrganizations && isLoadingOrganizations && (
              <div className="space-y-2">
                <Label className="text-foreground">Select Organization</Label>
                <div className="h-11 flex items-center justify-center border rounded-md border-border bg-muted">
                  <span className="text-sm text-muted-foreground">Loading organizations...</span>
                </div>
              </div>
            )}

            {/* Error state for organizations */}
            {shouldFetchOrganizations && organizationsError && (
              <div className="space-y-2">
                <Label className="text-foreground">Select Organization</Label>
                <div className="h-11 flex items-center justify-center border rounded-md border-destructive/50 bg-destructive/10">
                  <span className="text-sm text-destructive">Failed to load organizations</span>
                </div>
              </div>
            )}

            {/* Password Field */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="password" className="text-foreground">Password</Label>
                <button
                  type="button"
                  className="text-sm text-primary hover:text-primary/80 font-medium cursor-pointer"
                  onClick={handleForgotPasswordClick}
                >
                  Forgot password?
                </button>
              </div>
              <Input
                id="password"
                type="password"
                placeholder="Password"
                value={password}
                onChange={(e) => {
                  setPassword(e.target.value);
                  clearError('password');
                }}
                required
                className={`h-11 text-foreground placeholder:text-muted-foreground border-0 shadow-[inset_0_0_0_1px_hsl(var(--border))] focus-visible:!shadow-[inset_0_0_0_1px_hsl(var(--primary)),0_0_8px_hsl(var(--primary)/0.4)] focus:!shadow-[inset_0_0_0_1px_hsl(var(--primary)),0_0_8px_hsl(var(--primary)/0.4)] focus-visible:!ring-0 focus-visible:!outline-none focus:!ring-0 focus:!outline-none focus-visible:!ring-ring/0 ${errors.password ? 'shadow-[inset_0_0_0_1px_hsl(var(--destructive))]' : ''}`}
              />
              {errors.password && (
                <p className="text-sm text-destructive">{errors.password}</p>
              )}
            </div>

            {/* Login Button */}
            <Button
              type="submit"
              disabled={loginMutation.isPending || isLoading}
              className="w-full h-11 bg-primary hover:bg-primary/90 disabled:bg-primary/50 text-primary-foreground font-medium cursor-pointer"
            >
              {(loginMutation.isPending || isLoading) ? 'Logging in...' : 'Login'}
              {!(loginMutation.isPending || isLoading) && <ArrowIcon className="ml-2 w-4 h-4" />}
            </Button>
          </form>

          {/* Error Message */}
          {(loginMutation.error || errors.general) && (
            <div className="p-3 text-sm text-destructive bg-destructive/10 border border-destructive/20 rounded-md">
              {errors.general || loginMutation.error?.message}
            </div>
          )}

          {/* <SignUpPrompt onSignUpClick={handleSignUpClick} /> */}
        </div>
      </Card>

      <LoginFooter />
    </div>
  );
}