'use client';

import {
  createContext,
  useContext,
  useEffect,
  ReactNode,
} from 'react';
import { useUIStore } from '@/store/ui';

type Theme = 'light' | 'dark' | 'system';

interface ThemeContextValue {
  theme: Theme;
  setTheme: (theme: Theme) => void;
  actualTheme: 'light' | 'dark';
}

const ThemeContext = createContext<ThemeContextValue | undefined>(undefined);

interface ThemeProviderProps {
  children: ReactNode;
  defaultTheme?: Theme;
  storageKey?: string;
}

export function ThemeProvider({
  children,
}: ThemeProviderProps) {
  const { theme, setTheme: setStoreTheme } = useUIStore();
  
  // Calculate actual theme based on store theme
  const getActualTheme = (): 'light' | 'dark' => {
    if (typeof window === 'undefined') return 'light';
    if (theme === 'system') {
      return window.matchMedia('(prefers-color-scheme: dark)').matches
        ? 'dark'
        : 'light';
    }
    return theme as 'light' | 'dark';
  };
  
  const actualTheme = getActualTheme();

  useEffect(() => {
    const root = window.document.documentElement;
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const desiredIsDark =
      theme === 'system' ? mediaQuery.matches : theme === 'dark';
    const hasDark = root.classList.contains('dark');
    if (desiredIsDark && !hasDark) {
      root.classList.add('dark');
    } else if (!desiredIsDark && hasDark) {
      root.classList.remove('dark');
    }
  }, [theme]);

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

    const applySystem = () => {
      const root = window.document.documentElement;
      const isDark = mediaQuery.matches;
      const hasDark = root.classList.contains('dark');
      if (isDark && !hasDark) {
        root.classList.add('dark');
      } else if (!isDark && hasDark) {
        root.classList.remove('dark');
      }
    };

    const handleChange = () => {
      if (theme === 'system') {
        applySystem();
      }
    };

    mediaQuery.addEventListener('change', handleChange);
    if (theme === 'system') {
      applySystem();
    }
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [theme]);

  const handleSetTheme = (newTheme: Theme) => {
    setStoreTheme(newTheme);
  };

  const value: ThemeContextValue = {
    theme,
    setTheme: handleSetTheme,
    actualTheme,
  };

  return (
    <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}
