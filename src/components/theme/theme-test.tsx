'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ThemeToggle } from './theme-toggle';
import { useTheme } from './theme-provider';

export function ThemeTest() {
  const { actualTheme } = useTheme();

  return (
    <div className="min-h-screen bg-background p-8">
      <div className="max-w-4xl mx-auto space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-foreground">
              Kavia AI Theme Test
            </h1>
            <p className="text-muted-foreground mt-2">
              Current theme: <Badge variant="secondary">{actualTheme}</Badge>
            </p>
          </div>
          <ThemeToggle />
        </div>

        {/* Color Palette */}
        <Card>
          <CardHeader>
            <CardTitle>Color Palette</CardTitle>
            <CardDescription>
              Testing all theme colors in {actualTheme} mode
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="space-y-2">
                <div className="h-16 bg-primary rounded-lg flex items-center justify-center">
                  <span className="text-primary-foreground font-medium">Primary</span>
                </div>
                <p className="text-sm text-muted-foreground">Primary Color</p>
              </div>
              
              <div className="space-y-2">
                <div className="h-16 bg-secondary rounded-lg flex items-center justify-center">
                  <span className="text-secondary-foreground font-medium">Secondary</span>
                </div>
                <p className="text-sm text-muted-foreground">Secondary</p>
              </div>
              
              <div className="space-y-2">
                <div className="h-16 bg-muted rounded-lg flex items-center justify-center">
                  <span className="text-muted-foreground font-medium">Muted</span>
                </div>
                <p className="text-sm text-muted-foreground">Muted</p>
              </div>
              
              <div className="space-y-2">
                <div className="h-16 bg-accent rounded-lg flex items-center justify-center">
                  <span className="text-accent-foreground font-medium">Accent</span>
                </div>
                <p className="text-sm text-muted-foreground">Accent</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Components */}
        <Card>
          <CardHeader>
            <CardTitle>UI Components</CardTitle>
            <CardDescription>
              Testing various UI components with theme colors
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Buttons */}
            <div className="space-y-3">
              <h3 className="text-lg font-semibold text-foreground">Buttons</h3>
              <div className="flex flex-wrap gap-3">
                <Button variant="default">Primary Button</Button>
                <Button variant="secondary">Secondary Button</Button>
                <Button variant="outline">Outline Button</Button>
                <Button variant="ghost">Ghost Button</Button>
                <Button variant="destructive">Destructive Button</Button>
              </div>
            </div>

            {/* Badges */}
            <div className="space-y-3">
              <h3 className="text-lg font-semibold text-foreground">Badges</h3>
              <div className="flex flex-wrap gap-3">
                <Badge variant="default">Default</Badge>
                <Badge variant="secondary">Secondary</Badge>
                <Badge variant="outline">Outline</Badge>
                <Badge variant="destructive">Destructive</Badge>
              </div>
            </div>

            {/* Inputs */}
            <div className="space-y-3">
              <h3 className="text-lg font-semibold text-foreground">Inputs</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input placeholder="Enter your name..." />
                <Input placeholder="Enter your email..." type="email" />
              </div>
            </div>

            {/* Alerts */}
            <div className="space-y-3">
              <h3 className="text-lg font-semibold text-foreground">Alerts</h3>
              <div className="space-y-3">
                <Alert>
                  <AlertDescription>
                    This is a default alert message.
                  </AlertDescription>
                </Alert>
                <Alert variant="destructive">
                  <AlertDescription>
                    This is a destructive alert message.
                  </AlertDescription>
                </Alert>
                <Alert variant="success">
                  <AlertDescription>
                    This is a success alert message.
                  </AlertDescription>
                </Alert>
                <Alert variant="warning">
                  <AlertDescription>
                    This is a warning alert message.
                  </AlertDescription>
                </Alert>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Typography */}
        <Card>
          <CardHeader>
            <CardTitle>Typography</CardTitle>
            <CardDescription>
              Testing text colors and hierarchy
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <h1 className="text-4xl font-bold text-foreground">Heading 1</h1>
            <h2 className="text-3xl font-semibold text-foreground">Heading 2</h2>
            <h3 className="text-2xl font-medium text-foreground">Heading 3</h3>
            <p className="text-foreground">
              This is regular body text that should be easily readable in both light and dark themes.
            </p>
            <p className="text-muted-foreground">
              This is muted text that provides secondary information.
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
