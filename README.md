# Kavia Chatbot

A multi-tenant AI chatbot application built with Next.js 14+, designed to integrate seamlessly with the kavia.ai ecosystem.

## 🎯 Features

- **Multi-Tenant Architecture**: Support for multiple tenants with isolated data and customization
- **Mobile-First Design**: Responsive design optimized for mobile devices
- **Modern Tech Stack**: Next.js 14+, TypeScript, Tailwind CSS, Shadcn/ui
- **Real-time Chat**: Interactive chat interface with AI integration
- **Project Management**: Organize conversations by projects within tenants
- **Asset Integration**: Support for codebase, documents, and Figma files

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn

### Installation

1. Clone the repository:

```bash
git clone <repository-url>
cd kavia-chatbot
```

2. Install dependencies:

```bash
npm install
```

3. Run the development server:

```bash
npm run dev
```

4. Open your browser and navigate to:

- Demo tenant: [http://localhost:3000/demo/projects](http://localhost:3000/demo/projects)
- Test tenant: [http://localhost:3000/test/projects](http://localhost:3000/test/projects)

## 🏗️ Project Structure

```
src/
├── app/
│   ├── [tenant]/              # Multi-tenant routing
│   │   ├── login/            # Tenant-specific authentication
│   │   ├── projects/         # Project selection & management
│   │   ├── [project]/        # Project-specific routes
│   │   │   ├── chat/         # Main chat workspace
│   │   │   └── settings/     # Project settings
│   │   └── layout.tsx        # Tenant layout
│   └── api/                  # API routes with tenant support
├── components/
│   ├── ui/                   # Shadcn/ui components
│   ├── auth/                 # Authentication components
│   ├── tenant/               # Multi-tenant components
│   ├── projects/             # Project-related components
│   ├── chat/                 # Chat interface components
│   └── shared/               # Reusable components
├── hooks/                    # Custom React hooks
├── store/                    # Zustand stores
├── lib/                      # Utilities and configurations
├── middleware/               # Tenant resolution middleware
├── types/                    # TypeScript type definitions
└── styles/                   # Global styles
```

## 🛠️ Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Fix ESLint issues
- `npm run format` - Format code with Prettier
- `npm run format:check` - Check code formatting
- `npm run type-check` - Run TypeScript type checking

## 🎨 Tech Stack

- **Framework**: Next.js 14+ with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS + Shadcn/ui
- **State Management**: Zustand
- **Form Handling**: React Hook Form + Zod
- **Icons**: Lucide React
- **Development**: ESLint + Prettier

## 🌐 Multi-Tenant URL Structure

- `/{tenant}/projects` - Project selection
- `/{tenant}/{project}/chat` - Main chat workspace
- `/{tenant}/{project}/settings` - Project settings
- `/{tenant}/login` - Tenant-specific authentication

## 📱 Mobile Support

The application is built with mobile-first design principles:

- Touch-friendly interactions (44px minimum touch targets)
- Responsive breakpoints
- Safe area handling for notched devices
- Optimized for various screen sizes

## 🔧 Configuration

### Tenant Configuration

Valid tenants are configured in `src/middleware.ts`. To add a new tenant:

1. Add the tenant slug to the `VALID_TENANTS` array
2. Optionally customize tenant-specific settings

### Theme Customization

The application uses CSS variables for theming, defined in `src/app/globals.css`. Colors follow the kavia.ai brand guidelines.

## 📄 License

This project is part of the kavia.ai ecosystem.
